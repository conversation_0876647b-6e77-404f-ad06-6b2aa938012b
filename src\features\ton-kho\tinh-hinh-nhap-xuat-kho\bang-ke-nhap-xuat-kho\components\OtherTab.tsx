import { IconButton } from '@mui/material';
import { PopupFormField } from '@/components/arito/arito-form/pop-up/arito-popup-form-field';
import AritoIcon from '@/components/arito/arito-icon';

const mauLocOption = [
  { value: 0, label: 'Lưu mẫu mới', icon: 7, onClick: () => {} },
  { value: 1, label: 'Lưu đè vào mẫu đang chọn', icon: 75, onClick: () => {} },
  { value: 2, label: 'Xoá mẫu đang chọn', icon: 8, onClick: () => {} }
];

const mauPhanTichOption = [
  { value: 0, label: 'Tạo mẫu phân tích mới', icon: 7, onClick: () => {} },
  { value: 1, label: 'Sửa mẫu đang chọn', icon: 9, onClick: () => {} },
  { value: 2, label: 'Xoá mẫu đang chọn', icon: 8, onClick: () => {} }
];

export const OtherTab = ({ formMode }: { formMode: 'add' | 'edit' | 'view' }) => {
  return (
    <div className='p-4'>
      <div className='grid grid-cols-1 gap-x-8 space-y-2 lg:grid-cols-1 lg:space-y-0'>
        {/* Thông tin chi tiết */}
        <div className='space-y-2'>
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Tk vật tư'
            name='tk_vat_tu'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Tk hạch toán'
            name='tk_hach_toan'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Tk đối ứng'
            name='tk_doi_ung'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Tk giá vốn'
            name='tk_gia_von'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Mã nhập xuất'
            name='ma_nhap_xuat'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='text'
            label='Mã giao dịch'
            name='ma_giao_dich'
            disabled={formMode === 'view'}
            withSearch={true}
          />
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='select'
            label='Nhóm theo'
            name='nhom_theo'
            disabled={formMode === 'view'}
            options={[
              { label: 'Không phân nhóm', value: 0 },
              { label: 'Loại vật tư', value: 1 },
              { label: 'Nhóm vật tư 1', value: 2 },
              { label: 'Nhóm vật tư 2', value: 3 },
              { label: 'Nhóm vật tư 3', value: 4 },
              { label: 'Tài khoản vật tư', value: 5 }
            ]}
          />
          <PopupFormField
            className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
            type='select'
            label='Tính ps điều chuyển'
            name='tinh_ps_dieu_chuyen'
            disabled={formMode === 'view'}
            options={[
              { label: 'Không tính phát sinh điều chuyển kho', value: 0 },
              { label: 'Tính phát sinh điều chuyển kho', value: 1 }
            ]}
          />
          <div className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[1fr,1fr]'>
            <PopupFormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='select'
              label='Mẫu lọc báo cáo'
              name='mau_loc_bao_cao'
              disabled={formMode === 'view'}
              options={[{ label: 'Người dùng tự lọc', value: 0 }]}
            />
            <div className='relative'>
              <div className='group relative'>
                <IconButton
                  size='small'
                  color='primary'
                  disabled={formMode === 'view'}
                  className='square-full border border-gray-300'
                  style={{ width: '32px', height: '32px', borderRadius: '4px' }}
                >
                  <AritoIcon icon={624} />
                </IconButton>
                <div className='absolute left-0 z-10 hidden bg-white shadow-lg group-hover:block'>
                  {mauLocOption.map(option => (
                    <button
                      key={option.value}
                      className='flex w-full items-center px-4 py-1 text-sm text-gray-700 hover:bg-gray-100'
                      onClick={option.onClick}
                    >
                      {option.icon ? <AritoIcon icon={option.icon} /> : null}
                      <div className='ml-2'>{option.label}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
          <div className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[1fr,1fr]'>
            <PopupFormField
              className='grid grid-cols-[1fr] items-center lg:grid lg:grid-cols-[160px,1fr]'
              type='select'
              label='Mẫu phân tích DL'
              name='mau_phan_tich_dl'
              disabled={formMode === 'view'}
              options={[
                { label: 'Không phân tích', value: 0 },
                { label: 'Mẫu phân tích nhập xuất theo đối tượng', value: 1 },
                { label: 'Mẫu phân tích nhập xuất theo kho', value: 2 }
              ]}
            />
            <div className='relative'>
              <div className='group relative'>
                <IconButton
                  size='small'
                  color='primary'
                  disabled={formMode === 'view'}
                  className='square-full border border-gray-300'
                  style={{ width: '32px', height: '32px', borderRadius: '4px' }}
                >
                  <AritoIcon icon={790} />
                </IconButton>
                <div className='absolute left-0 z-10 hidden bg-white shadow-lg group-hover:block'>
                  {mauPhanTichOption.map(option => (
                    <button
                      key={option.value}
                      className='flex w-full items-center px-4 py-1 text-sm text-gray-700 hover:bg-gray-100'
                      onClick={option.onClick}
                    >
                      {option.icon ? <AritoIcon icon={option.icon} /> : null}
                      <div className='ml-2'>{option.label}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
