import React from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-6 p-4'>
      <div className='space-y-4'>
        {/* Thời gian tờ khai */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Thời gian tờ khai</Label>
          <FormField
            name='thoi_gian_to_khai'
            type='select'
            className='w-48'
            options={[
              { value: 'to_khai_thang', label: 'Tờ khai tháng' },
              { value: 'to_khai_quy', label: 'Tờ khai quý' },
              { value: 'to_khai_nam', label: 'Tờ khai năm' }
            ]}
            disabled={formMode === 'view'}
          />
        </div>

        {/* Tháng */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Tháng</Label>
          <FormField
            name='thang'
            type='number'
            className='w-20'
            disabled={formMode === 'view'}
          />
        </div>

        {/* Năm */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Năm</Label>
          <FormField
            name='nam'
            type='number'
            className='w-24'
            disabled={formMode === 'view'}
          />
        </div>

        {/* Chi tiết */}
        <div className='border-t pt-4'>
          <Label className='text-sm font-medium text-blue-600'>Chi tiết</Label>
        </div>

        {/* Loại tờ khai */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Loại tờ khai</Label>
          <div className='flex space-x-4'>
            <RadioButton
              name='loai_to_khai'
              options={[
                { value: 'to_khai_lan_dau', label: 'Tờ khai lần đầu' },
                { value: 'to_khai_bo_sung', label: 'Tờ khai bổ sung' }
              ]}
            />
          </div>
        </div>

        {/* Chọn báo cáo */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Chọn báo cáo</Label>
          <FormField
            name='chon_bao_cao'
            type='select'
            className='w-64'
            options={[
              { value: 'mau_thong_tu_80_2021', label: 'Mẫu thông tư 80/2021' },
              { value: 'mau_thong_tu_156_2013', label: 'Mẫu thông tư 156/2013' }
            ]}
            disabled={formMode === 'view'}
          />
          <span className='ml-2 text-sm text-gray-500'>...</span>
        </div>

        {/* Mẫu báo cáo */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Mẫu báo cáo</Label>
          <FormField
            name='mau_bao_cao'
            type='select'
            className='w-48'
            options={[
              { value: 'mau_tien_chuan', label: 'Mẫu tiền chuẩn' },
              { value: 'mau_ngoai_te', label: 'Mẫu ngoại tệ' }
            ]}
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
