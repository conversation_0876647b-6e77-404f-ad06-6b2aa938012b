import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';

export const useRowSelection = () => {
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);

  const handleRowClick = (params: GridRowParams) => {
    const row = params.row as any;
    setSelectedObj(row);
    // Make sure the row has a uuid property before using it
    if (row && row.uuid) {
      setSelectedRowIndex(row.uuid.toString());
    }
  };

  const clearSelection = () => {
    setSelectedObj(null);
    setSelectedRowIndex(null);
  };

  return {
    selectedObj,
    selectedRowIndex,
    handleRowClick,
    clearSelection
  };
};
