'use client';

import AritoDataTables from '@/components/custom/arito/data-tables';
import { useDocumentDialog } from '../../hooks/useDocumentDialog';
import { DocumentColumns } from '../../cols-definition';
import DocumentDialog from '../document-dialog';
import { FormMode } from '@/types/form';
import { useRows } from '@/hooks';

interface DocumentTabProps {
  formMode: FormMode;
}

export const DocumentTab = ({ formMode }: DocumentTabProps) => {
  const { selectedObj, selectedRowIndex, handleRowClick } = useRows();

  // Use the custom hook for managing document dialog state
  const {
    isOpen,
    formMode: dialogFormMode,
    openAddDialog,
    openEditDialog,
    openViewDialog,
    closeDialog
  } = useDocumentDialog();

  const documentData = [
    { id: '1', ma_nk: 'Q001', ten_nk: 'Quyển 1', so_ct: '001', ngay_ct: '2023-01-01' },
    { id: '2', ma_nk: 'Q002', ten_nk: 'Quyển 2', so_ct: '002', ngay_ct: '2023-01-02' },
    { id: '3', ma_nk: 'Q003', ten_nk: 'Quyển 3', so_ct: '003', ngay_ct: '2023-01-03' }
  ];

  const handleSubmit = (data: any) => {
    console.log('Document data submitted:', data);
    closeDialog();
  };

  const tables = [
    {
      name: '',
      rows: documentData,
      columns: DocumentColumns(openViewDialog)
    }
  ];

  return (
    <>
      <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
      {isOpen && (
        <DocumentDialog
          formMode={dialogFormMode}
          open={isOpen}
          initialData={selectedObj}
          onSubmit={handleSubmit}
          onClose={closeDialog}
          onAdd={openAddDialog}
          onEdit={() => selectedObj && openEditDialog()}
          onDelete={() => {}}
          onCopy={() => selectedObj && openAddDialog()}
        />
      )}
    </>
  );
};
