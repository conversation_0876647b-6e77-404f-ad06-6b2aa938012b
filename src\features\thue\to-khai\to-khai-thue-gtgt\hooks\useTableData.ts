import { useState } from 'react';
import { bangKeBanRaColumns, pl142Columns, vatTaxDeclarationColumns, vatTaxDeclarationPL43Columns } from '../cols-definition';

export function useTableData() {
  const [tables] = useState([
    {
      name: 'Tờ khai thuế GTGT (01/GTGT)(TT80/2021)',
      rows: [],
      columns: vatTaxDeclarationColumns
    },
    {
      name: 'Tờ khai thuế GTGT PL43',
      rows: [],
      columns: vatTaxDeclarationPL43Columns
    },
    {
      name: 'PL_GiamThue_GTGT_23_24',
      rows: [],
      columns: vatTaxDeclarationPL43Columns
    },
    {
      name: 'Bảng kê bán ra 01-1/GTGT',
      rows: [],
      columns: bangKeBanRaColumns
    },
    {
      name: 'Tờ khai thuế GTGT PL142',
      rows: [],
      columns: pl142Columns
    },
    {
      name: '<PERSON><PERSON>ng kê mua vào 01-2/GTGT',
      rows: [],
      columns: pl142Columns
    }
  ]);

  const handleRowClick = (row: any) => {
    console.log('Row clicked:', row);
  };

  const updateTableColumns = (templateValue: string) => {
    console.log('Update table columns:', templateValue);
  };

  return {
    tables,
    handleRowClick,
    updateTableColumns
  };
}
