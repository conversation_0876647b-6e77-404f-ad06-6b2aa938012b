import AritoIcon from '@/components/arito/arito-icon';
import { NavMenu } from '../types';

export const thueMenu: NavMenu = {
  title: 'Thuế',
  items: [
    {
      title: 'Tờ khai',
      items: [
        {
          title: 'Tờ khai thuế GTGT (01/GTGT)(TT80/2021)',
          href: '/thue/to-khai/to-khai-thue-gtgt'
      },
        {
          title: '05/KK - Tờ khai thuế TNCN (TT80/2021)',
          href: '/thue/to-khai/to-khai-thue-tncn'
        }
      ]
    },
    {
      title: '<PERSON>ậ<PERSON> bá<PERSON> cáo',
      items: []
    },
    {
      title: '<PERSON>hai báo',
      items: []
    },
    {
      title: 'Kiểm tra sót thuế',
      items: []
    },
    {
      title: 'Bảng kê thuế',
      items: [
        {
          title: 'Bảng kê hóa đơn, chứng từ hàng hóa, dịch vụ mua vào',
          icon: <AritoIcon icon={311} />,
          href: '/thue/bang-ke-thue/bang-ke-hoa-don-chung-tu-hang-hoa-dich-vu-mua-vao'
        },
        {
          title: 'Bảng kê hóa đơn, chứng từ hàng hóa, dịch vụ bán ra',
          icon: <AritoIcon icon={311} />,
          href: '/thue/bang-ke-thue/bang-ke-hoa-don-chung-tu-hang-hoa-dich-vu-ban-ra'
        }
      ]
    }
  ]
};
