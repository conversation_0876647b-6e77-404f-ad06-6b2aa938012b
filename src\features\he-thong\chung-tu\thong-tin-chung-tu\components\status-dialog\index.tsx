'use client';

import { <PERSON><PERSON> } from '@mui/material';
import { useState } from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import ConfirmDialog from '../../components/ConfirmDialog';
import AritoIcon from '@/components/custom/arito/icon';
import { BasicInfoTab } from './BasicInfoTab';
import { ChungTu } from '@/types/schemas';
import { FormSchema } from '../../schema';
import { FormMode } from '@/types/form';

interface StatusDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit: (data: any) => void;
  onClose: () => void;
  onEdit: () => void;
}

const StatusDialog = ({ open, onClose, onSubmit, onEdit, formMode, initialData }: StatusDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [chungTu, setChungTu] = useState<ChungTu | null>(initialData?.ma_ct_data || null);

  const handleSubmit = (data: any) => {
    onSubmit(data);
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const viewActions = [
    { onClick: onEdit, icon: 9, text: 'Sửa' },
    { onClick: onClose, icon: 885, text: 'Đóng' }
  ];

  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={formMode === 'add' ? 'Thêm' : formMode === 'edit' ? 'Sửa' : 'Danh mục trạng thái chứng từ'}
        maxWidth='xl'
        disableBackdropClose={false}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={formMode}
          hasAritoActionBar={false}
          schema={FormSchema}
          onSubmit={handleSubmit}
          initialData={initialData}
          className='h-full w-[900px]'
          headerFields={
            <div className='max-h-[calc(100vh-150px)] overflow-y-auto' onChange={() => setIsFormDirty(true)}>
              <BasicInfoTab formMode={formMode} chungTu={chungTu} setChungTu={setChungTu} />
            </div>
          }
          classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
          bottomBar={
            <>
              {formMode === 'view' && (
                <>
                  {viewActions.map(({ onClick, icon, text }) => (
                    <Button key={text} onClick={onClick} variant='outlined'>
                      <AritoIcon icon={icon} className='mr-2' />
                      {text}
                    </Button>
                  ))}
                </>
              )}
              {formMode !== 'view' && (
                <>
                  <Button
                    type='submit'
                    variant='contained'
                    className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
                  >
                    <AritoIcon icon={884} className='mr-2' />
                    Đồng ý
                  </Button>
                  <Button onClick={onClose} variant='outlined'>
                    <AritoIcon icon={885} className='mr-2' />
                    Huỷ
                  </Button>
                </>
              )}
            </>
          }
        />
      </AritoDialog>

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default StatusDialog;
