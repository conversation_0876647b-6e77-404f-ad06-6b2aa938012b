import { useState, useEffect } from 'react';
import type { HeSoPhanBo, HeSoPhanBoInput, HeSoPhanBoResponse } from '@/types/schemas';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

interface UseDoiTuongNhanPhanBoReturn {
  doiTuongList: HeSoPhanBo[];
  isLoading: boolean;
  addDoiTuong: (data: HeSoPhanBoInput) => Promise<HeSoPhanBo>;
  updateDoiTuong: (uuid: string, data: HeSoPhanBoInput) => Promise<HeSoPhanBo>;
  deleteDoiTuong: (uuid: string) => Promise<void>;
  refreshDoiTuongList: () => Promise<void>;
  error: string | null;
}

export const useDoiTuongNhanPhanBo = (
  initialList: HeSoPhanBo[] = [],
  searchData?: any
): UseDoiTuongNhanPhanBoReturn => {
  const [doiTuongList, setDoiTuongList] = useState<HeSoPhanBo[]>(initialList);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const { entity } = useAuth();

  const fetchDoiTuongList = async () => {
    if (!entity?.slug) return;

    setIsLoading(true);
    try {
      let url = `/entities/${entity.slug}/erp/${QUERY_KEYS.HE_SO_PHAN_BO}`;
      if (searchData) {
        const { ky, nam } = searchData;
        url += `?ky=${ky}&nam=${nam}/`;
      }
      const response = await api.get<HeSoPhanBoResponse>(url);
      setDoiTuongList(response.data.results);
    } catch (error: any) {
      console.error('Error fetching: ', error);
      setError(error.message || 'Có lỗi xảy ra khi tải dữ liệu');
    } finally {
      setIsLoading(false);
    }
  };

  const addDoiTuong = async (data: HeSoPhanBoInput): Promise<HeSoPhanBo> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.post<HeSoPhanBo>(`/entities/${entity.slug}/erp/${QUERY_KEYS.HE_SO_PHAN_BO}/`, data);
      setDoiTuongList(prev => [...prev, response.data]);
      return response.data;
    } catch (error: any) {
      console.error('Error adding: ', error);
      setError(error.message || 'Có lỗi xảy ra khi thêm dữ liệu');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateDoiTuong = async (uuid: string, data: HeSoPhanBoInput): Promise<HeSoPhanBo> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.put<HeSoPhanBo>(
        `/entities/${entity.slug}/erp/${QUERY_KEYS.HE_SO_PHAN_BO}/${uuid}/`,
        data
      );
      setDoiTuongList(prev => prev.map(item => (item.uuid === uuid ? response.data : item)));
      return response.data;
    } catch (error) {
      console.error('Error updating: ', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteDoiTuong = async (uuid: string): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${QUERY_KEYS.HE_SO_PHAN_BO}/${uuid}/`);
      setDoiTuongList(prev => prev.filter(item => item.uuid !== uuid));
    } catch (error) {
      console.error('Error deleting: ', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshDoiTuongList = async (): Promise<void> => {
    await fetchDoiTuongList();
  };

  useEffect(() => {
    fetchDoiTuongList();
  }, [entity?.slug]);

  return {
    doiTuongList,
    error,
    isLoading,
    addDoiTuong,
    updateDoiTuong,
    deleteDoiTuong,
    refreshDoiTuongList
  };
};
