import React from 'react';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import InputTableActionBar from '../../InputTableActionBar';
import { getDocumentInfoColumns } from './columns';

import { GridCellParams } from '@mui/x-data-grid';
import { FormMode } from '@/types/form';

interface SelectedCellInfo {
  id: string;
  field: string;
}

interface InfoTabProps {
  mode: FormMode;
  rows: any[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onPasteRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onExport: () => void;
  onPin: () => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

export const InfoTab: React.FC<InfoTabProps> = ({
  mode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onPasteRow,
  onMoveRow,
  onExport,
  onPin,
  onCellValueChange
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDocumentInfoColumns(onCellValueChange)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <InputTableActionBar
          mode={mode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handlePasteRow={onPasteRow}
          handleMoveRow={onMoveRow}
          handleExport={onExport}
          handlePin={onPin}
        />
      }
    />
  );
};
