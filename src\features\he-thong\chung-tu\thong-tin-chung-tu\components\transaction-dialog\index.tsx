'use client';

import { <PERSON><PERSON> } from '@mui/material';
import { useState } from 'react';
import { AritoHeaderTabs } from '@/components/custom/arito/header-tabs';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import ConfirmDialog from '../../components/ConfirmDialog';
import AritoIcon from '@/components/custom/arito/icon';
import { BasicInfoTab } from './BasicInfoTab';
import { ChungTu } from '@/types/schemas';
import { FormSchema } from '../../schema';
import { DefaultTab } from './DefaultTab';
import { useDefaultRows } from './hooks';
import { FormMode } from '@/types/form';

interface TransactionDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: any;
  onSubmit: (data: any) => void;
  onClose: () => void;
  onAdd: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onCopy: () => void;
  onView: () => void;
}

const TransactionDialog = ({
  open,
  onClose,
  onSubmit,
  onAdd,
  onEdit,
  onDelete,
  onCopy,
  onView,
  formMode,
  initialData
}: TransactionDialogProps) => {
  const [isConfirm, setIsConfirm] = useState<boolean>(false);
  const [isFormDirty, setIsFormDirty] = useState<boolean>(false);
  const [chungTu, setChungTu] = useState<ChungTu | null>(initialData?.ma_ct_data || null);
  const {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  } = useDefaultRows();

  const handleSubmit = (data: any) => {
    onSubmit(data);
    setIsFormDirty(false);
    onClose();
  };

  const handleClose = () => {
    if (isFormDirty && formMode !== 'view') {
      setIsConfirm(true);
    } else {
      onClose();
    }
  };

  const viewActions = [
    { onClick: onAdd, icon: 571, text: 'Thêm' },
    { onClick: onEdit, icon: 9, text: 'Sửa' },
    { onClick: onDelete, icon: 8, text: 'Xoá' },
    { onClick: onCopy, icon: 11, text: 'Sao chép' },
    { onClick: onView, icon: 10, text: 'Xem' },
    { onClick: onClose, icon: 885, text: 'Đóng' }
  ];

  return (
    <>
      <AritoDialog
        open={open}
        onClose={handleClose}
        title={formMode === 'add' ? 'Mới' : formMode === 'edit' ? 'Sửa' : 'Danh mục giao dịch'}
        maxWidth='xl'
        disableBackdropClose={false}
        disableEscapeKeyDown={true}
        titleIcon={<AritoIcon icon={281} />}
      >
        <AritoForm
          mode={formMode}
          hasAritoActionBar={false}
          schema={FormSchema}
          onSubmit={handleSubmit}
          initialData={initialData}
          className='h-full w-[900px]'
          headerFields={
            <div className='max-h-[calc(100vh-150px)] overflow-y-auto' onChange={() => setIsFormDirty(true)}>
              <BasicInfoTab formMode={formMode} chungTu={chungTu} setChungTu={setChungTu} />

              <AritoHeaderTabs
                tabs={[
                  {
                    id: 'default',
                    label: 'Giá trị mặc định',
                    component: (
                      <DefaultTab
                        mode={formMode}
                        rows={rows}
                        selectedRowUuid={selectedRowUuid}
                        onRowClick={handleRowClick}
                        onPin={() => console.log('Pin clicked')}
                        onCellValueChange={handleCellValueChange}
                      />
                    )
                  }
                ]}
              />
            </div>
          }
          classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
          bottomBar={
            <>
              {formMode === 'view' && (
                <>
                  {viewActions.map(({ onClick, icon, text }) => (
                    <Button key={text} onClick={onClick} variant='outlined'>
                      <AritoIcon icon={icon} className='mr-2' />
                      {text}
                    </Button>
                  ))}
                </>
              )}
              {formMode !== 'view' && (
                <>
                  <Button
                    type='submit'
                    variant='contained'
                    className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
                  >
                    <AritoIcon icon={884} className='mr-2' />
                    Đồng ý
                  </Button>
                  <Button onClick={onClose} variant='outlined'>
                    <AritoIcon icon={885} className='mr-2' />
                    Huỷ
                  </Button>
                </>
              )}
            </>
          }
        />
      </AritoDialog>

      {isConfirm && (
        <ConfirmDialog
          open={isConfirm}
          onClose={() => setIsConfirm(false)}
          onConfirm={() => {
            setIsConfirm(false);
            setIsFormDirty(false);
            onClose();
          }}
          title='Cảnh báo'
          content='Bạn muốn kết thúc?'
        />
      )}
    </>
  );
};

export default TransactionDialog;
