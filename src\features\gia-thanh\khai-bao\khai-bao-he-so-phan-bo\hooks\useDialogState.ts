import { useState } from 'react';

export const useDialogState = (clearSelection: () => void) => {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showWatchDialog, setShowWatchDialog] = useState(false);
  const [isCopyMode, setIsCopyMode] = useState(false);

  const openAddDialog = () => {
    setShowAddDialog(true);
    setIsCopyMode(false);
  };

  const closeAddDialog = () => {
    setShowAddDialog(false);
    clearSelection();
  };

  const openEditDialog = () => {
    setShowEditDialog(true);
  };

  const closeEditDialog = () => {
    setShowEditDialog(false);
    clearSelection();
  };

  const openDeleteDialog = () => {
    setShowDeleteDialog(true);
  };

  const closeDeleteDialog = () => {
    setShowDeleteDialog(false);
  };

  const openWatchDialog = () => {
    setShowWatchDialog(true);
  };

  const closeWatchDialog = () => {
    setShowWatchDialog(false);
    clearSelection();
  };

  const handleAddButtonClick = () => {
    setShowWatchDialog(false);
    setShowAddDialog(true);
    setIsCopyMode(false);
  };

  const handleEditButtonClick = () => {
    setShowWatchDialog(false);
    setShowEditDialog(true);
  };

  const handleDeleteButtonClick = () => {
    setShowWatchDialog(false);
    setShowDeleteDialog(true);
  };

  const handleCopyButtonClick = () => {
    setShowWatchDialog(false);
    setShowAddDialog(true);
    setIsCopyMode(true);
  };

  return {
    showAddDialog,
    showEditDialog,
    showDeleteDialog,
    showWatchDialog,
    isCopyMode,

    openAddDialog,
    closeAddDialog,
    openEditDialog,
    closeEditDialog,
    openDeleteDialog,
    closeDeleteDialog,
    openWatchDialog,
    closeWatchDialog,

    handleAddButtonClick,
    handleEditButtonClick,
    handleDeleteButtonClick,
    handleCopyButtonClick
  };
};
