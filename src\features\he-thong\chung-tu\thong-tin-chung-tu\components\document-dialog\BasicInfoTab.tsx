'use client';

import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { chungTuSearchColumns } from '@/constants/search-columns';
import QUERY_KEYS from '@/constants/query-keys';
import { QuyenChungTu } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  chungTu: QuyenChungTu | null;
  setChungTu: (chungTu: QuyenChungTu) => void;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode, chungTu, setChungTu }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-32 min-w-32'><PERSON><PERSON> quyển</Label>
          <SearchField<QuyenChungTu>
            type='text'
            name='ma_nk'
            disabled={true}
            searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}/`}
            searchColumns={chungTuSearchColumns}
            dialogTitle='Danh mục chứng từ'
            columnDisplay='ma_nk'
            displayRelatedField='ten_nk'
            classNameRelatedField='w-full'
            onRowSelection={setChungTu}
            value={chungTu?.ma_nk || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tên quyển</Label>
          <div className='w-full'>
            <FormField type='text' name='ten_nk' disabled={formMode === 'view'} value={chungTu?.ten_nk || ''} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tên khác</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='ten_nk2' disabled={formMode === 'view'} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
