import { z } from 'zod';

export const FormSchema = z.object({
  ma_yt: z.string().optional(),
  ma_bp: z.string().optional(),
  ma_sp: z.string().optional(),
  ma_vt: z.string().optional(),
  ma_lsx: z.string().optional(),
  ma_bp0: z.string().optional(),
  he_so: z.coerce.number().optional()
});

export type FormValues = z.infer<typeof FormSchema>;

export const initialFormValues: FormValues = {
  ma_yt: '',
  ma_bp: '',
  ma_vt: '',
  ma_lsx: '',
  ma_bp0: '',
  he_so: 0
};
