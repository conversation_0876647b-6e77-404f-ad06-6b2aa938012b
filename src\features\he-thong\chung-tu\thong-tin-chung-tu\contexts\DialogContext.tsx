'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { FormMode } from '@/types/form';

// Define types for each dialog
interface TransactionDialogState {
  isOpen: boolean;
  formMode: FormMode;
}

interface DocumentDialogState {
  isOpen: boolean;
  formMode: FormMode;
}

interface StatusDialogState {
  isOpen: boolean;
  formMode: FormMode;
}

// Context type definition
interface DialogContextType {
  // Transaction dialog state
  transactionDialog: TransactionDialogState;
  openTransactionDialog: (formMode: FormMode) => void;
  closeTransactionDialog: () => void;

  // Document dialog state
  documentDialog: DocumentDialogState;
  openDocumentDialog: (formMode: FormMode) => void;
  closeDocumentDialog: () => void;

  // Status dialog state
  statusDialog: StatusDialogState;
  openStatusDialog: (formMode: FormMode) => void;
  closeStatusDialog: () => void;
}

// Create the context
const DialogContext = createContext<DialogContextType | undefined>(undefined);

// Provider component
export const DialogProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Transaction dialog state
  const [transactionDialog, setTransactionDialog] = useState<TransactionDialogState>({
    isOpen: false,
    formMode: 'view'
  });

  // Document dialog state
  const [documentDialog, setDocumentDialog] = useState<DocumentDialogState>({
    isOpen: false,
    formMode: 'view'
  });

  // Status dialog state
  const [statusDialog, setStatusDialog] = useState<StatusDialogState>({
    isOpen: false,
    formMode: 'view'
  });

  // Transaction dialog handlers
  const openTransactionDialog = (formMode: FormMode) => {
    setTransactionDialog({
      isOpen: true,
      formMode
    });
  };

  const closeTransactionDialog = () => {
    setTransactionDialog({
      ...transactionDialog,
      isOpen: false
    });
  };

  // Document dialog handlers
  const openDocumentDialog = (formMode: FormMode) => {
    setDocumentDialog({
      isOpen: true,
      formMode
    });
  };

  const closeDocumentDialog = () => {
    setDocumentDialog({
      ...documentDialog,
      isOpen: false
    });
  };

  // Status dialog handlers
  const openStatusDialog = (formMode: FormMode) => {
    setStatusDialog({
      isOpen: true,
      formMode
    });
  };

  const closeStatusDialog = () => {
    setStatusDialog({
      ...statusDialog,
      isOpen: false
    });
  };

  return (
    <DialogContext.Provider
      value={{
        transactionDialog,
        openTransactionDialog,
        closeTransactionDialog,
        documentDialog,
        openDocumentDialog,
        closeDocumentDialog,
        statusDialog,
        openStatusDialog,
        closeStatusDialog
      }}
    >
      {children}
    </DialogContext.Provider>
  );
};

// Custom hook to use the dialog context
export const useDialogContext = () => {
  const context = useContext(DialogContext);
  if (context === undefined) {
    throw new Error('useDialogContext must be used within a DialogProvider');
  }
  return context;
};
