'use client';

import { useDialogContext } from '../contexts/DialogContext';

export const useDocumentDialog = () => {
  const { documentDialog, openDocumentDialog, closeDocumentDialog } = useDialogContext();

  const openAddDialog = () => {
    openDocumentDialog('add');
  };

  const openEditDialog = () => {
    openDocumentDialog('edit');
  };

  const openViewDialog = () => {
    openDocumentDialog('view');
  };

  return {
    isOpen: documentDialog.isOpen,
    formMode: documentDialog.formMode,
    openAddDialog,
    openEditDialog,
    openViewDialog,
    closeDialog: closeDocumentDialog
  };
};
