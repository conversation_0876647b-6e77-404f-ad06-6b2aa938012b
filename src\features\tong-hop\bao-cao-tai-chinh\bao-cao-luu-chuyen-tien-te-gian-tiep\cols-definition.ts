import { GridColDef } from '@mui/x-data-grid';

// Search columns for employee search
export const employeeSearchColumns: GridColDef[] = [
  { field: 'ma_nv', headerName: 'Mã nhân viên', width: 150 },
  { field: 'ten_nv', headerName: 'Tên nhân viên', width: 250 },
  { field: 'phong_ban', headerName: 'Phòng ban', width: 200 },
  { field: 'chuc_vu', headerName: 'Chức vụ', width: 150 }
];

// Search columns for unit search
export const unitSearchColumns: GridColDef[] = [
  { field: 'ma_unit', headerName: 'Mã đơn vị', width: 150 },
  { field: 'ten_unit', headerName: 'Tên đơn vị', width: 250 },
  { field: 'dia_chi', headerName: 'Địa chỉ', width: 300 }
];

// Common hidden fields for all report templates
const commonHiddenFields: GridColDef[] = [
  { field: 'xchi_tieu', headerName: 'Xchi tiêu', width: 0 },
  { field: 'xchi_tieu2', headerName: 'Xchi tiêu 2', width: 0 },
  { field: 'tk_du', headerName: 'TK dư', width: 0 },
  { field: 'dau_cuoi', headerName: 'Đầu cuối', width: 0 }
];

// 1. "Mẫu tiền chuẩn - năm" (value: '20')
export const standardYearColumns: GridColDef[] = [
  { field: 'chi_tieu', headerName: 'Chỉ tiêu', width: 350 },
  { field: 'ma_so', headerName: 'Mã số', width: 100 },
  { field: 'thuyet_minh', headerName: 'Thuyết minh', width: 150 },
  { field: 'tk', headerName: 'Tài khoản', width: 120 },
  { field: 'tk_du', headerName: 'Tài khoản đối ứng', width: 120 },
  { field: 'ky_nay', headerName: 'Kỳ này', width: 150 },
  { field: 'ky_truoc', headerName: 'Kỳ trước', width: 150 }
];

// 2. "Mẫu tiền chuẩn - giữa biên độ" (value: '21')
export const standardInterimColumns: GridColDef[] = [
  { field: 'chi_tieu', headerName: 'Chỉ tiêu', width: 350 },
  { field: 'ma_so', headerName: 'Mã số', width: 100 },
  { field: 'thuyet_minh', headerName: 'Thuyết minh', width: 150 },
  { field: 'tk', headerName: 'Tài khoản', width: 120 },
  { field: 'tk_du', headerName: 'Tài khoản đối ứng', width: 120 },
  { field: 'ky_nay', headerName: 'Kỳ này', width: 150 },
  { field: 'ky_truoc', headerName: 'Kỳ trước', width: 150 },
  { field: 'luy_ke_ky_nay', headerName: 'Lũy kế kỳ này', width: 150 },
  { field: 'luy_ke_ky_truoc', headerName: 'Lũy kế kỳ trước', width: 150 }
];

// 3. "Mẫu tiền nt - năm" (value: '30')
export const foreignCurrencyYearColumns: GridColDef[] = [
  { field: 'chi_tieu', headerName: 'Chỉ tiêu', width: 350 },
  { field: 'ma_so', headerName: 'Mã số', width: 100 },
  { field: 'thuyet_minh', headerName: 'Thuyết minh', width: 150 },
  { field: 'tk', headerName: 'Tài khoản', width: 120 },
  { field: 'tk_du', headerName: 'Tài khoản đối ứng', width: 120 },
  { field: 'ky_nay_nt', headerName: 'Kỳ này NT', width: 150 },
  { field: 'ky_truoc_nt', headerName: 'Kỳ trước NT', width: 150 },
  { field: 'ky_nay', headerName: 'Kỳ này', width: 150 },
  { field: 'ky_truoc', headerName: 'Kỳ trước', width: 150 }
];

// 4. "Mẫu tiền nt - giữa biên độ" (value: '31')
export const foreignCurrencyInterimColumns: GridColDef[] = [
  { field: 'chi_tieu', headerName: 'Chỉ tiêu', width: 350 },
  { field: 'ma_so', headerName: 'Mã số', width: 100 },
  { field: 'thuyet_minh', headerName: 'Thuyết minh', width: 150 },
  { field: 'tk', headerName: 'Tài khoản', width: 120 },
  { field: 'tk_du', headerName: 'Tài khoản đối ứng', width: 120 },
  { field: 'ky_nay_nt', headerName: 'Kỳ này NT', width: 150 },
  { field: 'ky_truoc_nt', headerName: 'Kỳ trước NT', width: 150 },
  { field: 'luy_ke_ky_nay_nt', headerName: 'Lũy kế kỳ này NT', width: 150 },
  { field: 'luy_ke_ky_truoc_nt', headerName: 'Lũy kế kỳ trước NT', width: 150 },
  { field: 'ky_nay', headerName: 'Kỳ này', width: 150 },
  { field: 'ky_truoc', headerName: 'Kỳ trước', width: 150 },
  { field: 'luy_ke_ky_nay', headerName: 'Lũy kế kỳ này', width: 150 },
  { field: 'luy_ke_ky_truoc', headerName: 'Lũy kế kỳ trước', width: 150 }
];

// Map template values to column definitions
export const reportTemplateColumnsMap: Record<string, GridColDef[]> = {
  '20': standardYearColumns,
  '21': standardInterimColumns,
  '30': foreignCurrencyYearColumns,
  '31': foreignCurrencyInterimColumns
};

// Default columns (for backward compatibility)
export const indirectCashFlowReportColumns = standardYearColumns;

// Sample data for the indirect cash flow report
export const sampleIndirectCashFlowData = [
  {
    ma_so: '01',
    chi_tieu: 'I. Lưu chuyển tiền từ hoạt động kinh doanh',
    thuyet_minh: '',
    tk: '',
    ky_nay: '',
    ky_truoc: '',
    ky_nay_nt: '',
    ky_truoc_nt: '',
    luy_ke_ky_nay: '',
    luy_ke_ky_truoc: '',
    luy_ke_ky_nay_nt: '',
    luy_ke_ky_truoc_nt: '',
    xchi_tieu: 'I',
    xchi_tieu2: 'LCTTHDKD',
    tk_du: '',
    dau_cuoi: '0'
  },
  {
    ma_so: '02',
    chi_tieu: '1. Tiền thu từ bán hàng, cung cấp dịch vụ và doanh thu khác',
    thuyet_minh: '01',
    tk: '511,512',
    ky_nay: '1,000,000',
    ky_truoc: '800,000',
    ky_nay_nt: '50,000',
    ky_truoc_nt: '40,000',
    luy_ke_ky_nay: '3,000,000',
    luy_ke_ky_truoc: '2,400,000',
    luy_ke_ky_nay_nt: '150,000',
    luy_ke_ky_truoc_nt: '120,000',
    xchi_tieu: '1',
    xchi_tieu2: 'TTBH',
    tk_du: 'C',
    dau_cuoi: '0'
  },
  {
    ma_so: '03',
    chi_tieu: '2. Tiền chi trả cho người cung cấp hàng hóa và dịch vụ',
    thuyet_minh: '02',
    tk: '331,641',
    ky_nay: '(600,000)',
    ky_truoc: '(500,000)',
    ky_nay_nt: '(30,000)',
    ky_truoc_nt: '(25,000)',
    luy_ke_ky_nay: '(1,800,000)',
    luy_ke_ky_truoc: '(1,500,000)',
    luy_ke_ky_nay_nt: '(90,000)',
    luy_ke_ky_truoc_nt: '(75,000)',
    xchi_tieu: '2',
    xchi_tieu2: 'CTNCC',
    tk_du: 'N',
    dau_cuoi: '0'
  },
  {
    ma_so: '04',
    chi_tieu: '3. Tiền chi trả cho người lao động',
    thuyet_minh: '03',
    tk: '334',
    ky_nay: '(200,000)',
    ky_truoc: '(180,000)',
    ky_nay_nt: '(10,000)',
    ky_truoc_nt: '(9,000)',
    luy_ke_ky_nay: '(600,000)',
    luy_ke_ky_truoc: '(540,000)',
    luy_ke_ky_nay_nt: '(30,000)',
    luy_ke_ky_truoc_nt: '(27,000)',
    xchi_tieu: '3',
    xchi_tieu2: 'CTNLD',
    tk_du: 'N',
    dau_cuoi: '0'
  },
  {
    ma_so: '20',
    chi_tieu: 'Lưu chuyển tiền thuần từ hoạt động kinh doanh',
    thuyet_minh: '',
    tk: '',
    ky_nay: '200,000',
    ky_truoc: '120,000',
    ky_nay_nt: '10,000',
    ky_truoc_nt: '6,000',
    luy_ke_ky_nay: '600,000',
    luy_ke_ky_truoc: '360,000',
    luy_ke_ky_nay_nt: '30,000',
    luy_ke_ky_truoc_nt: '18,000',
    xchi_tieu: 'T',
    xchi_tieu2: 'LCTTHDKD',
    tk_du: '',
    dau_cuoi: '1'
  }
];
