import { GridColDef } from '@mui/x-data-grid';

// Column definitions for VAT tax declaration
export const vatTaxDeclarationColumns: GridColDef[] = [
  { field: 'so_ct', headerName: '<PERSON><PERSON> chứng từ', width: 120 },
  { field: 'ngay_ct', headerName: '<PERSON><PERSON><PERSON> chứng từ', width: 120, type: 'date' },
  { field: 'thang', headerName: 'Tháng', width: 80, type: 'number' },
  { field: 'nam', headerName: 'Năm', width: 80, type: 'number' },
  { field: 'loai_to_khai', headerName: 'Loại tờ khai', width: 150 },
  { field: 'dien_giai', headerName: '<PERSON>ễn giải', width: 200 },
  { field: 'trang_thai', headerName: 'Trạng thái', width: 120 },
  { field: 'nguoi_tao', headerName: 'Người tạo', width: 120 },
  { field: 'ngay_tao', headerName: '<PERSON><PERSON><PERSON> tạo', width: 120, type: 'date' }
];

// Search columns for department lookup
export const boPhanSearchColumns: GridColDef[] = [
  { field: 'ma_bp', headerName: 'Mã bộ phận', width: 120 },
  { field: 'ten_bp', headerName: 'Tên bộ phận', width: 200 }
];
