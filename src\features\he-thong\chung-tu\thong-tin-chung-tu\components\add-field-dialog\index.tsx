'use client';

import { But<PERSON> } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { FormValues, FormSchema } from '../../schema';
import { BasicInfoTab } from './BasicInfoTab';
import { FormMode } from '@/types/form';
import { useInfoRows } from './hooks';
import { InfoTab } from './InfoTab';

interface AddFieldDialogProps {
  formMode: FormMode;
  open: boolean;
  initialData?: FormValues;
  onSubmit?: (data: FormValues) => void;
  onClose: () => void;
}

const AddFieldDialog = ({ open, onClose, onSubmit, formMode, initialData }: AddFieldDialogProps) => {
  const {
    rows,
    selectedRowUuid,
    handleRowClick,
    handleAddRow,
    handleDeleteRow,
    handleCopyRow,
    handlePasteRow,
    handleMoveRow,
    handleCellValueChange
  } = useInfoRows();

  const handleSubmit = (data: FormValues) => {
    onSubmit?.(data);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={'Thêm các thông tin trên chứng từ'}
      maxWidth='xl'
      disableBackdropClose={false}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode={formMode}
        hasAritoActionBar={false}
        schema={FormSchema}
        onSubmit={handleSubmit}
        initialData={initialData}
        className='h-full w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode={formMode} />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'info',
                  label: 'Thông tin',
                  component: (
                    <InfoTab
                      mode={formMode}
                      rows={rows}
                      selectedRowUuid={selectedRowUuid}
                      onRowClick={handleRowClick}
                      onAddRow={handleAddRow}
                      onDeleteRow={handleDeleteRow}
                      onCopyRow={handleCopyRow}
                      onPasteRow={handlePasteRow}
                      onMoveRow={handleMoveRow}
                      onExport={() => console.log('Export clicked')}
                      onPin={() => console.log('Pin clicked')}
                      onCellValueChange={handleCellValueChange}
                    />
                  )
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2 p-1'
        bottomBar={
          <>
            <Button
              type='submit'
              variant='contained'
              className='bg-[rgba(15,118,110,0.9)] normal-case hover:bg-[rgba(15,118,110,1)]'
            >
              <AritoIcon icon={884} className='mr-2' />
              Đồng ý
            </Button>
            <Button onClick={onClose} variant='outlined'>
              <AritoIcon icon={885} className='mr-2' />
              Huỷ
            </Button>
          </>
        }
      />
    </AritoDialog>
  );
};

export default AddFieldDialog;
