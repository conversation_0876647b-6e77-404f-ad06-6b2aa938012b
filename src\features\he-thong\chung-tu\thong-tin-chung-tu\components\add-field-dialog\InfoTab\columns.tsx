import { GridColDef } from '@mui/x-data-grid';

export const getDocumentInfoColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  { field: 'base_type', headerName: 'Sắp xếp', width: 100 },
  { field: 'base_field', headerName: 'Gần trường/cột', width: 250 },
  { field: 'field', headerName: 'Mã cột', width: 100 },
  { field: 'header', headerName: 'Tên cột', width: 100 },
  { field: 'header2', headerName: 'Tên 2', width: 100 },
  { field: 'data_type', headerName: 'Kiểu dữ liệu', width: 100 },
  { field: 'lookup_code', headerName: 'Chọn từ danh mục', width: 100 },
  { field: 'l_width', headerName: 'Độ rộng text', width: 100 },
  { field: 'width', headerName: 'Đ<PERSON> rộng', width: 100 },
  { field: 'df_value', headerName: '<PERSON><PERSON><PERSON> trị mặc định', width: 100 },
  { field: 'format', headerName: 'Định dạng', width: 100 },
  { field: 'align', headerName: 'Căn chỉnh', width: 100 },
  { field: 'a_bb_nhap', headerName: 'Bắt buộc nhập', width: 100 },
  { field: 'a_copy', headerName: 'Sao chép', width: 100 },
  { field: 'a_active', headerName: 'Nhảy vào nhập', width: 100 },
  { field: 'a_df_value', headerName: 'Mặc định phiếu trước/ dòng trên', width: 100 }
];
