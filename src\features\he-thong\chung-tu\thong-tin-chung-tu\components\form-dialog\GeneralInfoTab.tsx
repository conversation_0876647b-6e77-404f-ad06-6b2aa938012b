'use client';

import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface GeneralInfoTabProps {
  formMode: FormMode;
}

export const GeneralInfoTab: React.FC<GeneralInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'><PERSON><PERSON> thứ tự</Label>
          <div className='w-[100px]'>
            <FormField type='number' label='' name='stt' disabled={formMode === 'view'} />
          </div>
          <p>(Sử dụng để sắp xếp chứng từ trên 1 số báo cáo hoặc ưu tiên khi tính giá nhập trước xuất trước)</p>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số chứng từ hiện tại</Label>
          <div className='w-[100px]'>
            <FormField type='number' label='' name='i_so_ct' disabled={formMode === 'view'} />
          </div>
          <p>(Khi không sử dụng quyển chứng từ)</p>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số dòng tối đa</Label>
          <div className='w-[100px]'>
            <FormField type='number' label='' name='d_page_count' disabled={formMode === 'view'} />
          </div>
          <p>0. Không phân trang, (x &gt; 0). Phân trang trên chi tiết với x dòng 1 trang</p>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Thứ tự sắp xếp</Label>
          <div className='w-[300px]'>
            <FormField
              type='select'
              label=''
              name='order_type'
              disabled={formMode === 'view'}
              options={[
                { value: '0', label: 'Ưu tiên chứng từ mới nhập (Mặc định)' },
                { value: '1', label: 'Ưu tiên ngày chứng từ lớn nhất' }
              ]}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Trạng thái ngầm định</Label>
          <div className='w-[300px]'>
            <FormField
              type='select'
              label=''
              name='df_status'
              disabled={formMode === 'view'}
              options={[
                { value: '0', label: 'Lập chứng từ' },
                { value: '1', label: 'Chờ duyệt' },
                { value: '4', label: 'Đang duyệt' },
                { value: '5', label: 'Đã duyệt' },
                { value: '6', label: 'Đang thực hiện' },
                { value: '7', label: 'Hoàn thành' },
                { value: '9', label: 'Đóng' }
              ]}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'></Label>
          <div className='w-[250px]'>
            <FormField
              type='checkbox'
              label='Hiển thị thông tin người tạo'
              name='user_id0_yn'
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'></Label>
          <div className='w-[250px]'>
            <FormField
              type='checkbox'
              label='Hiển thị thông người sửa gần nhất'
              name='user_id2_yn'
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'></Label>
          <div className='w-[250px]'>
            <FormField
              type='checkbox'
              label='Hiển thị trường ngày lập chứng từ'
              name='ngay_lct_yn'
              disabled={formMode === 'view'}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Chứng từ liên quan</Label>
          <div className='w-full'>
            <FormField
              type='multiselect'
              label=''
              name='vc_link'
              disabled={formMode === 'view'}
              options={[
                { value: '0', label: 'Không lưu' },
                { value: '1', label: 'Lưu khi xóa' },
                { value: '2', label: 'Lưu khi xóa/sửa' }
              ]}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Lưu nhật ký</Label>
          <div className='w-[250px]'>
            <FormField
              type='select'
              label=''
              name='ct_save_log'
              disabled={formMode === 'view'}
              options={[
                { value: '0', label: 'Không lưu' },
                { value: '1', label: 'Lưu khi xóa' },
                { value: '2', label: 'Lưu khi xóa/sửa' }
              ]}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
