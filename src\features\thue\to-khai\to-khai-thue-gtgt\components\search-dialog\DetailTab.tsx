import React, { useState } from 'react';
import { RadioButton } from '@/components/custom/arito/form/radio-button';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface DetailTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const DetailTab: React.FC<DetailTabProps> = ({ formMode }) => {
  const boSungComponent = (
    <FormField 
      name='lan_bs' 
      type='number' 
      label='Bổ sung lần' 
      labelClassName='text-sm min-w-44' 
      className='min-w-44' 
      disabled={formMode === 'view'}
    />
  );

  const [boSung, setBoSung] = useState<React.ReactElement | null>(null);

  function setBoSungComponent(value: string) {
    if (value === 'to_khai_lan_dau') {
      setBoSung(null);
    }
    if (value === 'to_khai_bo_sung') {
      setBoSung(boSungComponent);
    }
  }

  return (
    <div className='space-y-6 p-4'>
      <div className='space-y-4'>
        {/* Loại tờ khai với radio buttons */}
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Loại tờ khai</Label>
          <div className='flex flex-col space-y-2'>
            <RadioButton
              name='loai_to_khai_detail'
              options={[
                { value: 'to_khai_lan_dau', label: 'Tờ khai lần đầu' },
                { value: 'to_khai_bo_sung', label: 'Tờ khai bổ sung' }
              ]}
              onChange={setBoSungComponent}
            />
          </div>
        </div>

        {/* Hiển thị field bổ sung nếu được chọn */}
        {boSung && (
          <div className='ml-32'>
            {boSung}
          </div>
        )}

        {/* Các trường khác */}
        <FormField 
          name='ghi_chu' 
          type='text' 
          label='Ghi chú' 
          labelClassName='text-sm min-w-32' 
          className='w-full'
          disabled={formMode === 'view'}
        />
      </div>
    </div>
  );
};

export default DetailTab;
