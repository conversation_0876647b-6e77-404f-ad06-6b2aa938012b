'use client';
import { GridRowParams } from '@mui/x-data-grid';
import { useState } from 'react';
import to from 'await-to-js';
import { createObject, deleteObject, updateObject } from '@/erpnext/erpnext-actions';
import AritoPopupForm from '@/components/arito/arito-form/pop-up/arito-popup-form';
import AritoConfirmModal from '@/components/arito/arito-confirm-modal';
import { AritoDataTables } from '@/components/arito/arito-data-tables';
import { FilterBySubJectTab } from '../components/FilterBySubjectTab';
import { bangKeNhapXuatKhoColumns } from '../cols-definition';
import { BasicInfoTab } from '../components/BasicInfoTab';
import { bangKeNhapXuatKhoSchema } from '../schemas';
import { ActionBar } from '../components/ActionBar';
import { DetailTab } from '../components/DetailTab';
import { OtherTab } from '../components/OtherTab';
import { formatYyyyMmDd } from '@/lib/utils';

export default function BangKeNhapXuatKhoPage({ initialRows }: { initialRows: any[] }) {
  // Form state
  const [showForm, setShowForm] = useState(true);
  const [formMode, setFormMode] = useState<'add' | 'edit' | 'view'>('add');
  const [selectedObj, setSelectedObj] = useState<any | null>(null);
  const [currentObj, setCurrentObj] = useState<any | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [selectedTable, setSelectedTable] = useState<'tien_chuan' | 'ngoai_te'>('tien_chuan');

  // Confirm modal state
  const [confirmModal, setConfirmModal] = useState({
    open: false,
    message: '',
    title: '',
    onConfirm: () => {}
  });

  //Initial
  const [rows, setRows] = useState<any[]>(
    initialRows.map(row => ({
      ...row,
      id: row.name // Use the name field as the id
    }))
  );

  const showErrorModal = (message: string) => {
    setConfirmModal({
      open: true,
      title: 'Lỗi',
      message: message,
      onConfirm: () => {
        setConfirmModal(prev => ({ ...prev, open: false }));
      }
    });
  };

  const showSuccessModal = (message: string, onOk?: () => void) => {
    setConfirmModal({
      open: true,
      title: 'Thành công',
      message: message,
      onConfirm: () => {
        setConfirmModal(prev => ({ ...prev, open: false }));
        if (onOk) onOk();
      }
    });
  };

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleOpenEditForm = (obj: any) => {
    setFormMode('edit');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenViewForm = (obj: any) => {
    setFormMode('view');
    setCurrentObj(obj);
    setShowForm(true);
  };

  const handleOpenAddForm = () => {
    setFormMode('add');
    setCurrentObj(null);
    setShowForm(true);
  };

  const handleRowClick = (params: GridRowParams) => {
    const order = params.row as any;
    setSelectedObj(order);
    setSelectedRowIndex(params.row.id.toString());
  };

  const handleFormSubmit = async (data: any) => {
    if (data.name) {
      console.log(`Name: ${data.name}`);
    } else {
      console.log('No name provided');
    }

    setSelectedTable(data.mau_bao_cao);

    // let [err, res] = [null, null];
    // switch (formMode) {
    //   case "add":
    //     [err, res] = await to(createObject("SoChiTietCongNo", data));
    //     break;
    //   case "edit":
    //     [err, res] = await to(updateObject("SoChiTietCongNo", data.name, data));
    //     break;
    // }

    // if (err) {
    //   console.log(err);
    //   showErrorModal(
    //     formMode === "add"
    //       ? "Tạo chi tiết công nợ thất bại"
    //       : "Cập nhật chi tiết công nợ thất bại",
    //   );
    //   return;
    // } else {
    //   console.log("OK");
    //   showSuccessModal(
    //     formMode === "add"
    //       ? "Tạo chi tiết công nợ thành công"
    //       : "Cập nhật chi tiết công nợ thành công",
    //     () => {
    //       window.location.reload();
    //     },
    //   );
    // }

    setShowForm(false);
  };

  const handleDelete = (obj: any) => {
    setConfirmModal({
      open: true,
      title: 'Cảnh báo',
      message: 'Bạn có chắc chắn muốn xoá chi tiết công nợ này không?',
      onConfirm: async () => {
        setConfirmModal(prev => ({ ...prev, open: false }));
        console.log('xoá');
        let [err, res] = await to(deleteObject('SoChiTietCongNo', obj.name));
        if (err) {
          console.log(err);
          showErrorModal('Xoá chi tiết công nợ thất bại');
          return;
        } else {
          showSuccessModal('Xoá chi tiết công nợ thành công', () => {
            window.location.reload();
          });
        }
      }
    });
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: rows,
      columns: bangKeNhapXuatKhoColumns(handleOpenViewForm, handleOpenEditForm)
    }
  ];

  return (
    <div className='flex h-full min-h-screen w-screen flex-col lg:overflow-hidden'>
      <AritoConfirmModal
        open={confirmModal.open}
        onClose={() => setConfirmModal(prev => ({ ...prev, open: false }))}
        onConfirm={confirmModal.onConfirm}
        title={confirmModal.title}
        message={confirmModal.message}
      />

      <AritoPopupForm<any>
        open={showForm}
        onClose={handleCloseForm}
        mode={formMode}
        title='Bảng kê nhập xuất kho'
        initialData={currentObj || undefined}
        onSubmit={handleFormSubmit}
        schema={bangKeNhapXuatKhoSchema}
        headerFields={<BasicInfoTab formMode={formMode} />}
        maxWidth='md'
        fullWidth={true}
        tabs={[
          {
            id: 'chi_tiet',
            label: 'Chi tiết',
            component: <DetailTab formMode={formMode} />
          },
          {
            id: 'loc_theo_doi_tuong',
            label: 'Lọc theo đối tượng',
            component: <FilterBySubJectTab formMode={formMode} />
          },
          {
            id: 'khac',
            label: 'Khác',
            component: <OtherTab formMode={formMode} />
          }
        ]}
      />

      <ActionBar
        onAddClick={handleOpenAddForm}
        onEditClick={() => selectedObj && handleOpenEditForm(selectedObj)}
        onViewClick={() => selectedObj && handleOpenViewForm(selectedObj)}
        onDeleteClick={() => {
          selectedObj && handleDelete(selectedObj);
        }}
        isEditDisabled={!selectedObj}
        isViewDisabled={!selectedObj}
        data={rows}
      />
      <div className='w-full overflow-hidden'>
        <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
      </div>
    </div>
  );
}
