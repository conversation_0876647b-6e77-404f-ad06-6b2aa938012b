import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';

export const bangKeNhapXuatKhoColumns = (
  handleOpenViewForm: (obj: any) => void,
  handleOpenEditForm: (obj: any) => void
): GridColDef[] => [
  { field: 'don_vi', headerName: 'Đơn vị', width: 120 },
  { field: 'ngay_ctu', headerName: 'Ngày c/từ', width: 120 },
  { field: 'so_ctu', headerName: 'Số c/từ', width: 120 },
  { field: 'so_hoa_don', headerName: 'Số hóa đơn', width: 120 },
  { field: 'dien_giai', headerName: '<PERSON>ễn giải', width: 200 },
  { field: 'ma_kho', headerName: 'Mã kho', width: 100 },
  { field: 'ma_vat_tu', headerName: '<PERSON><PERSON> vật tư', width: 100 },
  { field: 'ten_vat_tu', headerName: 'Tên vật tư', width: 150 },
  { field: 'tk_vat_tu', headerName: 'TK vật tư', width: 100 },
  { field: 'tk_doi_ung', headerName: 'TK đối ứng', width: 100 },
  { field: 'dvt', headerName: 'Đvt', width: 80 },
  { field: 'ma_lo', headerName: 'Mã lô', width: 100 },
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 100 },
  { field: 'ma_doi_tuong', headerName: 'Mã đối tượng', width: 120 },
  { field: 'don_gia', headerName: 'Đơn giá', width: 100 },
  { field: 'sl_nhap', headerName: 'Sl nhập', width: 100 },
  { field: 'tien_nhap', headerName: 'Tiền nhập', width: 120 },
  { field: 'sl_xuat', headerName: 'Sl xuất', width: 100 },
  { field: 'tien_xuat', headerName: 'Tiền xuất', width: 120 },
  { field: 'bo_phan', headerName: 'Bộ phận', width: 120 },
  { field: 'vu_viec', headerName: 'Vụ việc', width: 120 },
  { field: 'hop_dong', headerName: 'Hợp đồng', width: 120 },
  { field: 'dot_thanh_toan', headerName: 'Đợt thanh toán', width: 150 },
  { field: 'khe_uoc', headerName: 'Khế ước', width: 120 },
  { field: 'phi', headerName: 'Phí', width: 100 },
  { field: 'san_pham', headerName: 'Sản phẩm', width: 120 },
  { field: 'lenh_san_xuat', headerName: 'Lệnh sản xuất', width: 150 },
  { field: 'cp_khong_hle', headerName: 'C/p không h/lệ', width: 150 },
  { field: 'ma_ctu', headerName: 'Mã c/từ', width: 120 }
];
