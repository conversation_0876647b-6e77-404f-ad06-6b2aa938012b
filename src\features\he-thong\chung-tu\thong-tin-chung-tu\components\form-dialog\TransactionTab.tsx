'use client';

import { useTransactionDialog } from '../../hooks/useTransactionDialog';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { TransactionColumns } from '../../cols-definition';
import TransactionDialog from '../transaction-dialog';
import { FormMode } from '@/types/form';
import { useRows } from '@/hooks';

interface TransactionTabProps {
  formMode: FormMode;
}

export const TransactionTab = ({ formMode }: TransactionTabProps) => {
  const { selectedObj, selectedRowIndex, handleRowClick } = useRows();

  const {
    isOpen,
    formMode: dialogFormMode,
    openAddDialog,
    openEditDialog,
    openViewDialog,
    closeDialog
  } = useTransactionDialog();

  const handleSubmit = (data: any) => {
    console.log('Transaction data submitted:', data);
    closeDialog();
  };

  const tables = [
    {
      name: '',
      rows: [{ id: '1', ma_gd: 'GD001', ten_gd: 'Giao dịch 1', ten_gd2: 'Giao dịch 1', stt: '1337', status: '1' }],
      columns: TransactionColumns(openViewDialog)
    }
  ];

  return (
    <>
      <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
      {isOpen && (
        <TransactionDialog
          formMode={dialogFormMode}
          open={isOpen}
          initialData={selectedObj}
          onSubmit={handleSubmit}
          onClose={closeDialog}
          onAdd={openAddDialog}
          onEdit={() => selectedObj && openEditDialog()}
          onDelete={() => {}}
          onCopy={() => {}}
          onView={() => selectedObj && openViewDialog()}
        />
      )}
    </>
  );
};
