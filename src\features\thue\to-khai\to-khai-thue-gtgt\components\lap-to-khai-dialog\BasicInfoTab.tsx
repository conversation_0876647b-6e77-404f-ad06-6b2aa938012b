import React from 'react';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { boPhanSearchColumns } from '@/constants/search-columns';
import { <PERSON><PERSON><PERSON> } from '@/types/schemas';
import { Label } from '@/components/ui/label';

interface BasicInfoTabProps {
  formMode: 'add' | 'edit' | 'view';
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode }) => {
  return (
    <div className='space-y-6 p-4'>
      <div className='space-y-4'>
        <FormField 
          name='loai' 
          type='text' 
          label='Loại' 
          labelClassName='text-sm min-w-32' 
          disabled={true} 
        />
        
        <FormField 
          name='don_vi' 
          type='text' 
          label='Đơn vị' 
          labelClassName='text-sm min-w-32' 
          disabled={true} 
        />
        
        <div className='flex items-center'>
          <Label className='w-32 min-w-32 text-sm font-medium'>Bộ phận</Label>
          <SearchField<BoPhan>
            type='text'
            displayRelatedField={'ten_bp'}
            columnDisplay={'ma_bp'}
            className='w-[11.25rem]'
            searchEndpoint='/bo-phan/'
            searchColumns={boPhanSearchColumns}
            dialogTitle='Danh mục bộ phận'
            disabled={formMode === 'view'}
          />
          <FormField 
            name='so_ct' 
            type='text' 
            label='' 
            labelClassName='text-sm min-w-32' 
            disabled={formMode === 'view'}
          />
        </div>

        <FormField 
          name='ngay_ct' 
          type='date' 
          label='Ngày chứng từ' 
          labelClassName='text-sm min-w-32' 
          disabled={formMode === 'view'}
        />

        <FormField 
          name='dien_giai' 
          type='text' 
          label='Diễn giải' 
          labelClassName='text-sm min-w-32' 
          className='w-full'
          disabled={formMode === 'view'}
        />

        {/* Thông tin tờ khai */}
        <div className='border-t pt-4'>
          <Label className='text-sm font-medium text-blue-600'>Thông tin tờ khai</Label>
        </div>

        <FormField
          name='thoi_gian_to_khai'
          type='select'
          label='Thời gian tờ khai'
          labelClassName='text-sm min-w-32'
          className='w-48'
          options={[
            { value: 'to_khai_thang', label: 'Tờ khai tháng' },
            { value: 'to_khai_quy', label: 'Tờ khai quý' },
            { value: 'to_khai_nam', label: 'Tờ khai năm' }
          ]}
          disabled={formMode === 'view'}
        />

        <div className='flex space-x-4'>
          <FormField
            name='thang'
            type='number'
            label='Tháng'
            labelClassName='text-sm min-w-16'
            className='w-20'
            disabled={formMode === 'view'}
          />
          
          <FormField
            name='nam'
            type='number'
            label='Năm'
            labelClassName='text-sm min-w-16'
            className='w-24'
            disabled={formMode === 'view'}
          />
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
