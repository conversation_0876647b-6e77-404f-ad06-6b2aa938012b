import { useState } from 'react';
import { KhuVuc } from '@/types/schemas/khu-vuc.type';

type FormMode = 'add' | 'edit' | 'view';

export default function useFormState() {
  const [showForm, setShowForm] = useState(false);
  const [showSearchForm, setShowSearchForm] = useState(true);
  const [showDelete, setShowDelete] = useState(false);
  const [formMode, setFormMode] = useState<FormMode>('add');
  const [selectedObj, setSelectedObj] = useState<KhuVuc | null>(null);
  const [selectedRowIndex, setSelectedRowIndex] = useState<string | null>(null);
  const [isCopyMode, setIsCopyMode] = useState(false);

  const handleCloseForm = () => {
    setShowForm(false);
  };

  const handleCloseSearchForm = () => {
    setShowSearchForm(false);
  };
  const handleSearchClick = () => {
    setShowSearchForm(true);
  };

  const handleCloseDelete = () => {
    setShowDelete(false);
  };

  const handleAddClick = () => {
    setFormMode('add');
    setIsCopyMode(false);
    setShowForm(true);
  };

  const handleEditClick = () => {
    setFormMode('edit');
    setShowForm(true);
  };

  const handleViewClick = () => {
    setFormMode('view');
    setShowForm(true);
  };

  const handleDeleteClick = () => {
    setShowDelete(true);
  };

  const handleCopyClick = () => {
    setFormMode('add');
    setIsCopyMode(true);
    setShowForm(true);
  };

  const handleRowClick = (params: any) => {
    setSelectedRowIndex(params.id);
    setSelectedObj(params.row);
  };

  const clearSelection = () => {
    setSelectedRowIndex(null);
    setSelectedObj(null);
  };

  return {
    showForm,
    showDelete,
    formMode,
    selectedObj,
    selectedRowIndex,
    isCopyMode,
    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleViewClick,
    handleDeleteClick,
    handleCopyClick,
    handleRowClick,
    clearSelection,
    showSearchForm,
    handleCloseSearchForm,
    handleSearchClick
  };
}
