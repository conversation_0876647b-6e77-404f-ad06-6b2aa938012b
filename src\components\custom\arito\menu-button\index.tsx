import React, { ComponentType, ReactNode, useState } from 'react';
import { Menu, MenuItem, Divider } from '@mui/material';
import { LucideIcon } from 'lucide-react';
export interface MenuItemProps {
  title: string;
  icon: ReactNode | LucideIcon;
  onClick?: () => void;
  group?: number;
}

interface AritoMenuButtonProps {
  title?: string;
  items: MenuItemProps[];
  icon?: ReactNode | LucideIcon;
}

const AritoMenuButton: React.FC<AritoMenuButtonProps> = ({ title, items, icon }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    event.preventDefault();
    event.nativeEvent.stopImmediatePropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const groupedItems: { [key: number]: MenuItemProps[] } = {};
  items.forEach(item => {
    const group = item.group || 0;
    if (!groupedItems[group]) {
      groupedItems[group] = [];
    }
    groupedItems[group].push(item);
  });

  const groupKeys = Object.keys(groupedItems).map(Number).sort();

  return (
    <>
      <button
        onClick={handleClick}
        className='flex flex-col items-center justify-center rounded-[2.5px] bg-teal-700 px-3 py-1 font-semibold text-white transition duration-75 hover:bg-teal-800 hover:shadow-sm'
      >
        {icon ? (
          React.isValidElement(icon) ? (
            icon
          ) : (
            React.createElement(icon as ComponentType<{ className?: string }>, { className: 'text-white size-4' })
          )
        ) : (
          <svg
            xmlns='http://www.w3.org/2000/svg'
            width='20'
            height='20'
            viewBox='0 0 24 24'
            fill='none'
            stroke='currentColor'
            strokeWidth='2'
            strokeLinecap='round'
            strokeLinejoin='round'
          >
            <line x1='4' y1='6' x2='20' y2='6'></line>
            <line x1='4' y1='12' x2='20' y2='12'></line>
            <line x1='4' y1='18' x2='20' y2='18'></line>
          </svg>
        )}

        <span className='text-xs'>{title}</span>
      </button>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
        PaperProps={{
          style: {
            maxWidth: '420px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
            border: '1px solid #d0d0d0',
            borderRadius: '0',
            padding: 0
          }
        }}
        MenuListProps={{
          style: {
            padding: '3px 0'
          }
        }}
      >
        {groupKeys.map((group, groupIndex) => (
          <React.Fragment key={group}>
            {groupIndex > 0 && <Divider style={{ margin: '6px 0' }} />}
            {groupedItems[group].map((item, itemIndex) => (
              <MenuItem
                key={`${group}-${itemIndex}`}
                onClick={() => {
                  if (item.onClick) item.onClick();
                  handleClose();
                }}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1.5,
                  padding: '8px 10px',
                  minHeight: '32px',
                  fontSize: '13px',
                  borderRadius: 0,
                  '&:hover': {
                    backgroundColor: '#dcebfc'
                  }
                }}
              >
                {React.isValidElement(item.icon)
                  ? item.icon
                  : React.createElement(item.icon as ComponentType<{ className?: string }>, {
                      className: 'text-[#0b87c9] size-4'
                    })}
                <span className='text-gray-900'>{item.title}</span>
              </MenuItem>
            ))}
          </React.Fragment>
        ))}
      </Menu>
    </>
  );
};

export default AritoMenuButton;
