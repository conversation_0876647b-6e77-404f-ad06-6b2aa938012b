import { useState, useEffect } from 'react';

export const useSearchFieldStates = (initialData?: any) => {
  const [yeuTo, setYeuTo] = useState<any | null>(null);
  const [bo<PERSON>han, setBo<PERSON>han] = useState<any | null>(null);
  const [san<PERSON>ham, setSan<PERSON>ham] = useState<any | null>(null);
  const [vatTu, setVatTu] = useState<any | null>(null);
  const [lenhSanXuat, setlenhSanXuat] = useState<any | null>(null);
  const [boPhanGT, setBoPhanGT] = useState<any | null>(null);

  // Update state when initialData changes
  useEffect(() => {
    if (initialData) {
      // Set yeuTo from ma_yt_data if available
      if (initialData.ma_yt_data) {
        setYeuTo(initialData.ma_yt_data);
      }

      // Set boPhan from ma_bp_data if available
      if (initialData.ma_bp_data) {
        setBoPhan(initialData.ma_bp_data);
      }
      // Set SanPham from ma_sp_data if available
      if (initialData.ma_sp_data) {
        setSanPham(initialData.ma_sp_data);
      }
      // Set vatTu from ma_sp_data if available
      if (initialData.ma_vt_data) {
        setVatTu(initialData.ma_vt_data);
      }

      // Set lenhSanXuat from so_lsx_data if available
      if (initialData.so_lsx_data) {
        setlenhSanXuat(initialData.so_lsx_data);
      }

      // Set boPhanGT from ma_bp0_data if available
      if (initialData.ma_bp0_data) {
        setBoPhanGT(initialData.ma_bp0_data);
      }
    }
  }, [initialData]);

  return {
    yeuTo,
    setYeuTo,
    boPhan,
    setBoPhan,
    sanPham,
    setSanPham,
    vatTu,
    setVatTu,
    lenhSanXuat,
    setlenhSanXuat,
    boPhanGT,
    setBoPhanGT
  };
};
