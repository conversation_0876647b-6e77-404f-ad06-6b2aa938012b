'use client';

import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import ColorSelect from '@/components/custom/arito/form/color-select';
import { FormField } from '@/components/custom/arito/form/form-field';
import { chungTuSearchColumns } from '@/constants/search-columns';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { ChungTu } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  chungTu: ChungTu | null;
  setChungTu: (chungTu: ChungTu) => void;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode, chungTu, setChungTu }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        {formMode !== 'add' && (
          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Mã chứng từ</Label>
            <SearchField<ChungTu>
              type='text'
              name='ma_ct'
              disabled={true}
              searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}/`}
              searchColumns={chungTuSearchColumns}
              dialogTitle='Danh mục chứng từ'
              columnDisplay='ma_ct'
              displayRelatedField='ten_ct'
              classNameRelatedField='w-full'
              onRowSelection={setChungTu}
              value={chungTu?.ma_ct || ''}
            />
          </div>
        )}

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tên trạng thái chứng từ</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='ten_ttct' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tên khác</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='ten_ttct2' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Màu</Label>
          <div className='flex items-center'>
            <ColorSelect className='w-fit items-center' disabled={formMode === 'view'} />
            <FormField
              type='checkbox'
              label='Áp dụng cho tất cả trạng thái cùng mã của khác chứng từ'
              name='all_status'
              disabled={formMode === 'view'}
              defaultValue={false}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Stt sắp xếp</Label>
          <div className='w-[70px]'>
            <FormField type='number' label='' name='stt' disabled={formMode === 'view'} />
          </div>
          <Label>(Thứ tự sắp xếp khi chọn danh sách nhập liệu trên các chứng từ, sắp xếp từ nhỏ &gt; lớn)</Label>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Trạng thái</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='status'
              disabled={formMode === 'view'}
              options={[
                { label: '1. Còn sử dụng', value: '1' },
                { label: '0. Không sử dụng', value: '0' }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfoTab;
