import React from 'react';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import InputTableActionBar from '../../InputTableActionBar';
import { getDefaultValueColumns } from './columns';

import { GridCellParams } from '@mui/x-data-grid';
import { FormMode } from '@/types/form';

interface SelectedCellInfo {
  id: string;
  field: string;
}

interface DefaultTabProps {
  mode: FormMode;
  rows: any[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onPin: () => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

export const DefaultTab: React.FC<DefaultTabProps> = ({
  mode,
  rows,
  selectedRowUuid,
  onRowClick,
  onPin,
  onCellValueChange
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDefaultValueColumns(onCellValueChange)}
      getRowId={row => row?.uuid || ''}
      actionButtons={<InputTableActionBar mode={mode} handlePin={onPin} />}
    />
  );
};
