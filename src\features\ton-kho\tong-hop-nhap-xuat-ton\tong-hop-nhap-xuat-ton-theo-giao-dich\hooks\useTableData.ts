import { GridColDef, GridColumnGroupingModel } from '@mui/x-data-grid';
import { useState, useEffect } from 'react';
import {
  tongHopNhapXuatTonTheoGiaoDichMap,
  tongHopNhapXuatTonTheoGiaoDichGroupMap,
  mauSoLuongVaGiaTriColumns,
  mauSoLuongVaGiaTriGroup
} from '../cols-definition';

export interface TableData {
  name: string;
  columns: GridColDef[];
  rows: any[];
  columnGroupingModel?: GridColumnGroupingModel;
}

export interface UseTableDataReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
  updateTableColumns: (templateValue: string) => void;
}

export function useTableData(): UseTableDataReturn {
  const [tables, setTables] = useState<TableData[]>([
    {
      name: '',
      columns: mauSoLuongVaGiaTriColumns,
      rows: [],
      columnGroupingModel: mauSoLuongVaGiaTriGroup
    }
  ]);

  // Initialize with sample data and default columns
  useEffect(() => {
    const defaultTemplateValue = '1';
    const defaultColumns = tongHopNhapXuatTonTheoGiaoDichMap[defaultTemplateValue] || mauSoLuongVaGiaTriColumns;
    const defaultGroups = tongHopNhapXuatTonTheoGiaoDichGroupMap[defaultTemplateValue] || mauSoLuongVaGiaTriGroup;

    setTables([
      {
        name: '',
        columns: defaultColumns,
        rows: [],
        columnGroupingModel: defaultGroups
      }
    ]);
  }, []);

  // Update columns when template changes
  const updateTableColumns = (templateValue: string) => {
    console.log('Updating columns for template:', templateValue);
    const columns = tongHopNhapXuatTonTheoGiaoDichMap[templateValue] || tongHopNhapXuatTonTheoGiaoDichMap;
    const groups = tongHopNhapXuatTonTheoGiaoDichGroupMap[templateValue] || tongHopNhapXuatTonTheoGiaoDichGroupMap;

    // Force a re-render by creating a new array
    setTables([
      {
        name: '',
        columns: columns,
        columnGroupingModel: groups,
        rows: []
      }
    ]);
  };

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  return {
    tables,
    handleRowClick,
    updateTableColumns
  };
}
