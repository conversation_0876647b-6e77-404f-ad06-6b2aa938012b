/**
 * TypeScript interface for HeSoPhanBo (Allocation Factor Declaration) model
 *
 * This interface represents the structure of the HeSoPhanBo model from the backend.
 * It defines allocation factor declarations used in the cost accounting system.
 */

import { ApiResponse } from '../api.type';

/**
 * Interface for HeSoPhanBo (Allocation Factor Declaration) model
 */
export interface HeSoPhanBo {
  /**
   * Unique identifier (UUID)
   */
  uuid: string;

  /**
   * Reference to the entity model
   */
  entity_model: string;

  /**
   * Unit identifier
   */
  unit_id?: string | null;

  /**
   * Period (1-12)
   */
  ky: number;

  /**
   * Year
   */
  nam: number;

  /**
   * Factor code
   */
  ma_yt?: string | null;

  /**
   * Department code
   */
  ma_bp?: string | null;

  /**
   * Product code
   */
  ma_sp?: string | null;
  ma_vt?: string | null;

  /**
   * Production order code
   */
  ma_lsx: string;

  /**
   * Direct/Indirect department code
   */
  ma_bp0?: string | null;

  /**
   * Allocation coefficient or ratio
   */
  he_so?: number;

  /**
   * Status indicator ('1'=active, '0'=inactive)
   */
  status: string;

  /**
   * Timestamp of creation
   */
  created: string;

  /**
   * Timestamp of last update
   */
  updated: string;
}

/**
 * Type for HeSoPhanBo API response
 */
export type HeSoPhanBoResponse = ApiResponse<HeSoPhanBo>;

/**
 * Type for creating or updating a HeSoPhanBo
 */
export interface HeSoPhanBoInput {
  /**
   * Reference to the entity model
   */
  entity_model?: string;

  /**
   * Unit identifier (optional)
   */
  unit_id?: string | null;

  /**
   * Period (1-12)
   */
  ky: number;

  /**
   * Year
   */
  nam: number;

  /**
   * Factor code (optional)
   */
  ma_yt?: string | null;

  /**
   * Department code (optional)
   */
  ma_bp?: string | null;

  /**
   * Product code (optional)
   */
  ma_sp?: string | null;
  ma_vt?: string | null;

  /**
   * Production order code
   */
  ma_lsx?: string;

  /**
   * Direct/Indirect department code (optional)
   */
  ma_bp0?: string | null;

  /**
   * Allocation coefficient or ratio
   */
  he_so?: number;

  /**
   * Status indicator ('1'=active, '0'=inactive)
   */
  status?: string;
}
