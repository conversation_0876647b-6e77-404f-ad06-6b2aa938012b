'use client';

import { useDialogContext } from '../contexts/DialogContext';

export const useTransactionDialog = () => {
  const { transactionDialog, openTransactionDialog, closeTransactionDialog } = useDialogContext();

  const openAddDialog = () => {
    openTransactionDialog('add');
  };

  const openEditDialog = () => {
    openTransactionDialog('edit');
  };

  const openViewDialog = () => {
    openTransactionDialog('view');
  };

  return {
    isOpen: transactionDialog.isOpen,
    formMode: transactionDialog.formMode,
    openAddDialog,
    openEditDialog,
    openViewDialog,
    closeDialog: closeTransactionDialog
  };
};
