import { GridColDef, GridColumnGroupingModel } from '@mui/x-data-grid';

export const warehouseSearchColumns: GridColDef[] = [
  { field: 'ma_kho', headerName: 'Mã kho', width: 150 },
  { field: 'ten_kho', headerName: 'Tên kho', width: 250 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 200 },
  { field: 'vi_tri_yn', headerName: 'Theo dõi vị trí', width: 150 }
];

export const giaoDichSearchColumns: GridColDef[] = [
  { field: 'code', headerName: 'Mã giao dịch', width: 150 },
  { field: 'name', headerName: 'Tên giao dịch', width: 250 },
  { field: 'ma_ct', headerName: 'Mã chứng từ', width: 300 }
];

export const itemSearchColumns: GridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 150 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 250 },
  { field: 'dvt', headerName: 'Đvt', width: 150 },
  { field: 'nh_vt1', headerName: 'Nhóm 1', width: 150 },
  { field: 'lo_yn', headerName: 'Theo dõi lô', width: 150 },
  { field: 'qc_yn', headerName: 'Quy cách', width: 150 },
  { field: 'image_id', headerName: 'Hình ảnh', width: 150 }
];

export const loSearchColumns: GridColDef[] = [
  { field: 'ma_lo', headerName: 'Mã lô', width: 150 },
  { field: 'ten_lo', headerName: 'Tên lô', width: 250 }
];

export const viTriSearchColumns: GridColDef[] = [
  { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 150 },
  { field: 'ten_vi_tri', headerName: 'Tên Vị trí', width: 250 }
];

export const nhomVatTuSearchColumns: GridColDef[] = [
  { field: 'ma_nh', headerName: 'Mã nhóm', width: 150 },
  { field: 'ten_nh', headerName: 'Tên nhómnhóm', width: 250 }
];

export const taiKhoanSearchColumns: GridColDef[] = [
  { field: 'tk', headerName: 'Mã tài khoản', width: 150 },
  { field: 'ten_tk', headerName: 'Tên tài khoản', width: 250 },
  { field: 'tk_me', headerName: 'Tài khoản mẹ', width: 250 },
  { field: 'tk_sc', headerName: 'Tk sổ cái', width: 250 },
  { field: 'ct_yn', headerName: 'Tk chi tiết', width: 250 },
  { field: 'bac_tk', headerName: 'Bậc tk', width: 150 }
];

export const quyDoiDVTSearchColumns: GridColDef[] = [
  { field: 'dvt', headerName: 'Đvt', width: 150 },
  { field: 'ten_dvt', headerName: 'Tên đơn vị tính', width: 250 }
];

export const mauSoLuongColumns: GridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 100 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 100 },
  { field: 'dvt', headerName: 'Đvt', width: 150 },
  { field: 'so_luong', headerName: 'Số lượng', width: 120 }
];

export const mauSoLuongVaGiaTriColumns: GridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 100 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 100 },
  { field: 'dvt', headerName: 'Đvt', width: 150 },
  { field: 'so_luong', headerName: 'Số lượng', width: 120 },
  { field: 'gia_tri', headerName: 'Giá trị', width: 120 }
];

export const mauSoLuongVaGiaTriNTColumns: GridColDef[] = [
  { field: 'ma_vt', headerName: 'Mã vật tư', width: 100 },
  { field: 'ten_vt', headerName: 'Tên vật tư', width: 100 },
  { field: 'dvt', headerName: 'Đvt', width: 150 },
  { field: 'so_luong', headerName: 'Số lượng', width: 120 },
  { field: 'gia_tri', headerName: 'Giá trị', width: 120 },
  { field: 'gia_tri_nt', headerName: 'Giá trị nt', width: 120 }
];

export const mauSoLuongVaGiaTriGroup: GridColumnGroupingModel = [
  {
    headerClassName: 'text-sm text-gray-900 bg-gray-100 flex items-center justify-center',
    groupId: ' ',
    children: [
      { field: 'ma_vt', headerName: 'Mã vật tư' },
      { field: 'ten_vt', headerName: 'Tên vật tư' },
      { field: 'dvt', headerName: 'Đvt' }
    ]
  },
  {
    headerClassName: 'text-sm text-gray-900 bg-gray-100 flex items-center justify-center',
    groupId: 'Số lượng',
    children: [
      { field: 'so_luong', headerName: 'Số lượng' },
      { field: 'gia_tri', headerName: 'Giá trị' }
    ]
  }
];

export const mauSoLuongVaGiaTriNTGroup: GridColumnGroupingModel = [
  {
    headerClassName: 'text-sm text-gray-900 bg-gray-100 flex items-center justify-center',
    groupId: ' ',
    children: [
      { field: 'ma_vt', headerName: 'Mã vật tư' },
      { field: 'ten_vt', headerName: 'Tên vật tư' },
      { field: 'dvt', headerName: 'Đvt' }
    ]
  },
  {
    headerClassName: 'text-sm text-gray-900 bg-gray-100 flex items-center justify-center',
    groupId: 'Số lượng',
    children: [
      { field: 'so_luong', headerName: 'Số lượng' },
      { field: 'gia_tri', headerName: 'Giá trị' },
      { field: 'gia_tri_nt', headerName: 'Giá trị nt' }
    ]
  }
];
export const tongHopNhapXuatTonTheoGiaoDichMap: Record<string, any[]> = {
  '0': mauSoLuongColumns,
  '1': mauSoLuongVaGiaTriColumns,
  '2': mauSoLuongVaGiaTriNTColumns
};

export const tongHopNhapXuatTonTheoGiaoDichGroupMap: Record<string, any[]> = {
  '0': [],
  '1': mauSoLuongVaGiaTriGroup,
  '2': mauSoLuongVaGiaTriNTGroup
};
