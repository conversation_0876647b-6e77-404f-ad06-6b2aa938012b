import { z } from 'zod';

export const searchSchema = z.object({
  loai: z.string().optional(),
  don_vi: z.string().optional(),
  bo_phan: z.string().optional(),
  so_ct: z.string().optional(),
  ngay_ct: z.coerce.date().optional(),
  dien_giai: z.string().optional(),
  thoi_gian_to_khai: z.string().optional(),
  thang: z.coerce.number().optional(),
  nam: z.coerce.number().optional(),
  loai_to_khai: z.string().optional(),
  chon_bao_cao: z.string().optional(),
  mau_bao_cao: z.string().optional()
});

export const searchInitData = {
  loai: '',
  don_vi: '',
  bo_phan: '',
  so_ct: '',
  ngay_ct: null,
  dien_giai: '',
  thoi_gian_to_khai: 'to_khai_thang',
  thang: new Date().getMonth() + 1,
  nam: new Date().getFullYear(),
  loai_to_khai: 'to_khai_lan_dau',
  chon_bao_cao: 'mau_thong_tu_80_2021',
  mau_bao_cao: 'mau_tien_chuan'
};

export const lapToKhaiSchema = z.object({
  loai: z.string().optional(),
  don_vi: z.string().optional(),
  bo_phan: z.string().optional(),
  so_ct: z.string().optional(),
  ngay_ct: z.coerce.date().optional(),
  dien_giai: z.string().optional(),
  thoi_gian_to_khai: z.string().optional(),
  thang: z.coerce.number().optional(),
  nam: z.coerce.number().optional(),
  loai_to_khai: z.string().optional(),
  chon_bao_cao: z.string().optional(),
  mau_bao_cao: z.string().optional(),
  status: z.string().optional()
});

export const lapToKhaiInitData = {
  loai: 'Tờ khai thuế giá trị gia tăng',
  don_vi: 'CÔNG TY CỔ PHẦN SẢN XUẤT THƯƠNG MẠI DỊCH VỤ HƯƠNG TRÀ CÀ PHÊ',
  bo_phan: '',
  so_ct: '',
  ngay_ct: undefined,
  dien_giai: '',
  thoi_gian_to_khai: 'to_khai_thang',
  thang: new Date().getMonth() + 1,
  nam: new Date().getFullYear(),
  loai_to_khai: 'to_khai_lan_dau',
  chon_bao_cao: 'mau_thong_tu_80_2021',
  mau_bao_cao: 'mau_tien_chuan',
  status: '5'
};

export type SearchFormValues = z.infer<typeof searchSchema>;
export type LapToKhaiFormValues = z.infer<typeof lapToKhaiSchema>;
