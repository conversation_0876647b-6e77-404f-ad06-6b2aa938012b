'use client';

import AritoDataTables from '@/components/custom/arito/data-tables';
import { useStatusDialog } from '../../hooks/useStatusDialog';
import { StatusColumns } from '../../cols-definition';
import StatusDialog from '../status-dialog';
import { FormMode } from '@/types/form';
import { useRows } from '@/hooks';

interface StatusTabProps {
  formMode: FormMode;
}

export const StatusTab = ({ formMode }: StatusTabProps) => {
  const { selectedObj, selectedRowIndex, handleRowClick } = useRows();

  // Use the custom hook for managing status dialog state
  const {
    isOpen,
    formMode: dialogFormMode,
    openAddDialog,
    openEditDialog,
    openViewDialog,
    closeDialog
  } = useStatusDialog();

  const statusData = [
    { id: '1', ten_ttct: 'TT001', query_yn: '1', mau_ttct: '#FFFFFF' },
    { id: '2', ten_ttct: 'TT002', query_yn: '1', mau_ttct: '#F3F3F3' },
    { id: '3', ten_ttct: 'TT003', query_yn: '1', mau_ttct: '#E6E6E6' }
  ];

  const handleSubmit = (data: any) => {
    console.log('Status data submitted:', data);
    closeDialog();
  };

  const tables = [
    {
      name: '',
      rows: statusData,
      columns: StatusColumns(openViewDialog)
    }
  ];

  return (
    <>
      <AritoDataTables tables={tables} selectedRowId={selectedRowIndex || undefined} onRowClick={handleRowClick} />
      {isOpen && (
        <StatusDialog
          formMode={dialogFormMode}
          open={isOpen}
          initialData={selectedObj}
          onSubmit={handleSubmit}
          onClose={closeDialog}
          onEdit={() => selectedObj && openEditDialog()}
        />
      )}
    </>
  );
};
