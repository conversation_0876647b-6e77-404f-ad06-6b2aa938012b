'use client';

import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { chungTuSearchColumns } from '@/constants/search-columns';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { ChungTu } from '@/types/schemas';
import { FormMode } from '@/types/form';

interface BasicInfoTabProps {
  formMode: FormMode;
  chungTu: ChungTu | null;
  setChungTu: (chungTu: ChungTu) => void;
}

export const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ formMode, chungTu, setChungTu }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        {formMode !== 'add' && (
          <div className='flex items-center'>
            <Label className='w-32 min-w-32'>Mã chứng từ</Label>
            <SearchField<ChungTu>
              type='text'
              name='ma_ct'
              disabled={formMode === 'view'}
              searchEndpoint={`/${QUERY_KEYS.CHUNG_TU}/`}
              searchColumns={chungTuSearchColumns}
              dialogTitle='Danh mục chứng từ'
              columnDisplay='ma_ct'
              displayRelatedField='ten_ct'
              classNameRelatedField='w-full'
              onRowSelection={setChungTu}
              value={chungTu?.ma_ct || ''}
            />
          </div>
        )}

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Mã giao dịch</Label>
          <FormField type='text' label='' name='ma_gd' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tên giao dịch</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='ten_gd' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Tên khác</Label>
          <div className='w-full'>
            <FormField type='text' label='' name='ten_gd2' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Stt sắp xếp</Label>
          <div className='w-[70px]'>
            <FormField type='number' label='' name='stt' disabled={formMode === 'view'} />
          </div>
          <Label>(Sử dụng để sắp xếp chứng từ trên 1 số báo cáo hoặc ưu tiên khi tính giá nhập trước xuất trước)</Label>
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Trạng thái</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='status'
              disabled={formMode === 'view'}
              options={[
                { label: '1. Còn sử dụng', value: '1' },
                { label: '0. Không sử dụng', value: '0' }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
