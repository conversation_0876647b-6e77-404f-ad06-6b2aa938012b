import { <PERSON><PERSON> } from '@mui/material';
import React from 'react';
import { AritoHeaderTabs } from '@/components/custom/arito/header-tabs';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { SearchFormValues, searchSchema, searchInitData } from '../../schema';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import BasicInfoTab from './BasicInfoTab';
import DetailTab from './DetailTab';

interface SearchDialogProps {
  openSearchDialog: boolean;
  onCloseSearchDialog: () => void;
  onSearch: (data: SearchFormValues) => void;
  updateTableColumns?: (templateValue: string) => void;
}

const SearchDialog: React.FC<SearchDialogProps> = ({
  openSearchDialog,
  onCloseSearchDialog,
  onSearch,
  updateTableColumns
}) => {
  const handleSubmit = (data: SearchFormValues) => {
    // Call the onSearch callback with the form data
    onSearch(data);

    // Update table columns if needed
    if (updateTableColumns && data.mau_bao_cao) {
      updateTableColumns(data.mau_bao_cao);
    }

    // Close the dialog
    onCloseSearchDialog();
  };

  return (
    <AritoDialog
      open={openSearchDialog}
      onClose={onCloseSearchDialog}
      title='Tờ khai thuế giá trị gia tăng'
      maxWidth='xl'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        initialData={searchInitData}
        className='w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'chi_tiet',
                  label: 'Chi tiết',
                  component: <DetailTab formMode='add' />
                }
              ]}
            />
          </div>
        }
        bottomBar={
          <div className='flex justify-end space-x-2 p-4'>
            <Button variant='outlined' onClick={onCloseSearchDialog}>
              Hủy
            </Button>
            <Button variant='contained' color='primary' type='submit'>
              Tìm kiếm
            </Button>
          </div>
        }
      >
      </AritoForm>
    </AritoDialog>
  );
};

export default SearchDialog;
