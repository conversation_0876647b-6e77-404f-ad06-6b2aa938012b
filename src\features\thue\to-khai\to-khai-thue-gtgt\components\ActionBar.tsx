import { <PERSON>lip<PERSON><PERSON><PERSON><PERSON><PERSON>, Pin, Printer, Refresh<PERSON><PERSON>, Save, Search, Settings } from 'lucide-react';
import React from 'react';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoActionButton } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';
import { TableConfig } from '../hooks/useTableData';
import { SearchFormValues } from '../schema';

interface ActionBarProps {
  currentTable: TableConfig;
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onLapToKhaiClick?: () => void;
  onCoDinhCotClick?: () => void;
  onChinhSuaMauInClick?: () => void;
  searchParams?: SearchFormValues | null;
}

const ActionBar: React.FC<ActionBarProps> = ({
  currentTable,
  onSearchClick,
  onRefreshClick,
  onLapToKhaiClick,
  onCoDinhCotClick,
  onChinhSuaMauInClick,
  searchParams
}) => {
  const { actionBarConfig } = currentTable;

  return (
    <AritoActionBar
      titleComponent={
        <div className='text-lg font-semibold'>
          {currentTable.title}
          {searchParams && (
            <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
              <div className='mr-2 size-2 rounded-full bg-red-500' />
              Từ tháng {searchParams.thang} đến tháng {searchParams.thang} năm {searchParams.nam}
            </span>
          )}
        </div>
      }
    >
      <AritoActionButton icon={Search} onClick={onSearchClick} title='Tìm kiếm' variant='primary' />

      <AritoMenuButton title='In ấn' icon={Printer} items={[]} />

      {actionBarConfig.showLapToKhai && onLapToKhaiClick && (
        <AritoActionButton icon={Save} onClick={onLapToKhaiClick} title='Lập tờ khai' />
      )}

      {actionBarConfig.showTinhLai && <AritoActionButton icon={ClipboardPenLine} onClick={() => {}} title='Tính lại' />}

      {actionBarConfig.showCoDinhCot && onCoDinhCotClick && (
        <AritoActionButton icon={Pin} onClick={onCoDinhCotClick} title='Cố định cột' />
      )}

      {actionBarConfig.showChinhSuaMauIn && onChinhSuaMauInClick && (
        <AritoActionButton icon={Settings} onClick={onChinhSuaMauInClick} title='Chỉnh sửa mẫu in' />
      )}

      <AritoActionButton icon={RefreshCw} onClick={onRefreshClick} title='Refresh' />

      <AritoMenuButton
        title='Khác'
        items={[
          {
            icon: <AritoIcon icon={17} />,
            title: 'Xuất Excel',
            onClick: () => {},
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
