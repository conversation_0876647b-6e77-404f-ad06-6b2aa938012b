import { ClipboardPen<PERSON>ine, Printer, RefreshCw, Save, Search } from 'lucide-react';
import React from 'react';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { AritoActionButton } from '@/components/custom/arito';
import AritoIcon from '@/components/custom/arito/icon';
import { SearchFormValues } from '../schema';

interface ActionBarProps {
  onSearchClick: () => void;
  onRefreshClick: () => void;
  onLapToKhaiClick: () => void;
  searchParams?: SearchFormValues | null;
}

const ActionBar: React.FC<ActionBarProps> = ({ onSearchClick, onRefreshClick, onLapToKhaiClick, searchParams }) => {
  return (
    <AritoActionBar
      titleComponent={
        <div className='text-lg font-semibold'>
          T<PERSON> khai thuế giá trị gia tăng
          {searchParams && (
            <span className='flex items-center justify-start text-xs font-semibold text-gray-500'>
              <div className='mr-2 size-2 rounded-full bg-red-500' />
              Từ tháng {searchParams.thang} đến tháng {searchParams.thang} năm {searchParams.nam}
            </span>
          )}
        </div>
      }
    >
      <AritoActionButton icon={Search} onClick={onSearchClick} title='Tìm kiếm' variant='primary' />
      <AritoMenuButton title='In ấn' icon={Printer} items={[]} />
      <AritoActionButton icon={Save} onClick={onLapToKhaiClick} title='Lập tờ khai' />
      <AritoActionButton icon={ClipboardPenLine} onClick={() => {}} title='Tính lại' />
      <AritoActionButton icon={RefreshCw} onClick={onRefreshClick} title='Refresh' />
      <AritoMenuButton
        title='Khác'
        items={[
          {
            icon: <AritoIcon icon={16} />,
            title: 'Cố định cột',
            onClick: () => {},
            group: 0
          },
          {
            icon: <AritoIcon icon={17} />,
            title: 'Xuất Excel',
            onClick: () => {},
            group: 0
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
