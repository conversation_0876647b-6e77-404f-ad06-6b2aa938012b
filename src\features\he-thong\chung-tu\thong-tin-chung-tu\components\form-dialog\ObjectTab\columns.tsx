import { GridCellParams, GridColDef } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';

export const getObjectColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  { field: 'ten_dttd', headerName: 'Tên đối tượng', width: 250 },
  {
    field: 'gl_group_yn',
    headerName: 'Nhóm trong sổ cái',
    width: 50,
    type: 'boolean',
    renderCell: (params: GridCellParams) => {
      return (
        <Checkbox
          checked={params.row.gl_group_yn}
          onCheckedChange={(checked: boolean) => onCellValueChange(params.row.uuid, 'gl_group_yn', checked)}
        />
      );
    }
  },
  { field: 'a_bb_nhap', headerName: 'Bắt buộc nhập', width: 100 },
  { field: 'a_hidden', headerName: 'Ẩn/hiện', width: 100 },
  { field: 'a_copy', headerName: 'Sao chép', width: 100 },
  { field: 'a_active', headerName: 'Nh<PERSON>y và nhập', width: 100 },
  { field: 'a_df_value', headerName: 'Mặc định dòng trên', width: 100 },
  { field: 'df_value', headerName: 'Giá trị mặc định', width: 100 }
];
