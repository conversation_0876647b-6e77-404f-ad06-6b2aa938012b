import {
  congDoanSearchColumns,
  lenhSanXuatSearchColumns,
  vatTuSearchColumns,
  yeuToSearchColumns
} from '@/constants/search-columns';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import type { DanhMucCongDoan, VatTu } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface BasicInfoProps {
  formMode: 'add' | 'edit' | 'view';
  yeuTo?: any | null;
  setYeuTo?: (yeuTo: any) => void;
  boPhan?: DanhMucCongDoan | null;
  setBoPhan?: (boPhan: DanhMucCongDoan) => void;
  sanPham?: VatTu | null;
  setSanPham?: (vatTu: VatTu) => void;
  vatTu?: VatTu | null;
  setVatTu?: (vatTu: VatTu) => void;
  lenhSanXuat?: any | null;
  setLenhSanXuat?: (lenhSanXuat: any) => void;
  boPhanGT?: DanhMucCongDoan | null;
  setBoPhanGT?: (boPhan: DanhMucCongDoan) => void;
}

const BasicInfo = ({
  formMode,
  yeuTo,
  setYeuTo,
  boPhan,
  setBoPhan,
  sanPham,
  setSanPham,
  vatTu,
  setVatTu,
  lenhSanXuat,
  setLenhSanXuat,
  boPhanGT,
  setBoPhanGT
}: BasicInfoProps) => {
  const isViewMode = formMode === 'view';

  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='p-4 md:p-6'>
        <div className='flex flex-col gap-y-4 md:gap-y-6'>
          <div className='space-y-4 md:space-y-6'>
            {/* Mã yếu tố */}
            <div className='flex items-center'>
              <Label className='min-w-40'>Mã yếu tố</Label>
              <SearchField<any>
                dialogTitle='Danh mục yếu tố'
                searchEndpoint={`/${QUERY_KEYS.DANH_MUC_YEU_TO}`}
                searchColumns={yeuToSearchColumns}
                displayRelatedField={'ten_yt'}
                columnDisplay={'ma_yt'}
                value={yeuTo?.ma_yt || ''}
                relatedFieldValue={yeuTo?.ten_yt || ''}
                onRowSelection={setYeuTo}
                disabled={isViewMode}
              />
            </div>

            {/* Mã bộ phận */}
            <div className='flex items-center'>
              <Label className='min-w-40'>Mã bộ phận</Label>
              <SearchField<any>
                dialogTitle='Danh mục công đoạn'
                searchEndpoint={`/${QUERY_KEYS.DANH_MUC_CONG_DOAN}`}
                searchColumns={congDoanSearchColumns}
                displayRelatedField={'ma_bp_data.ten_bp'}
                columnDisplay={'ma_bp_data.ma_bp'}
                value={boPhan?.ma_bp_data?.ma_bp || ''}
                relatedFieldValue={boPhan?.ma_bp_data?.ten_bp || ''}
                onRowSelection={setBoPhan}
                disabled={isViewMode}
              />
            </div>

            {/* Mã vật tư */}
            <div className='flex items-center'>
              <Label className='min-w-40'>Mã sản phẩm</Label>
              <SearchField<VatTu>
                type='text'
                displayRelatedField={'ten_vt'}
                columnDisplay={'ma_vt'}
                searchEndpoint={`/${QUERY_KEYS.VAT_TU}`}
                searchColumns={vatTuSearchColumns}
                dialogTitle='Danh mục vật tư'
                value={sanPham?.ma_vt || ''}
                relatedFieldValue={sanPham?.ten_vt || ''}
                onRowSelection={setSanPham}
                disabled={isViewMode}
              />
            </div>

            {/* Mã vật tư */}
            <div className='flex items-center'>
              <Label className='min-w-40'>Mã vật tư</Label>
              <SearchField<VatTu>
                type='text'
                displayRelatedField={'ten_vt'}
                columnDisplay={'ma_vt'}
                searchEndpoint={`/${QUERY_KEYS.VAT_TU}`}
                searchColumns={vatTuSearchColumns}
                dialogTitle='Danh mục vật tư'
                value={vatTu?.ma_vt || ''}
                relatedFieldValue={vatTu?.ten_vt || ''}
                onRowSelection={setVatTu}
                disabled={isViewMode}
              />
            </div>

            {/* Số lệnh sx */}
            <div className='flex items-center'>
              <Label className='min-w-40'>Số lệnh sx</Label>
              <SearchField
                dialogTitle='Danh mục lệnh sản xuất'
                searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}`}
                displayRelatedField={'description'}
                columnDisplay={'ma_lsx'}
                searchColumns={lenhSanXuatSearchColumns}
                value={lenhSanXuat?.ma_lsx || ''}
                relatedFieldValue={lenhSanXuat?.description || ''}
                onRowSelection={setLenhSanXuat}
                disabled={isViewMode}
              />
            </div>

            {/* Bp gián tiếp */}
            <div className='flex items-center'>
              <Label className='min-w-40'>Bp gián tiếp</Label>
              <SearchField<any>
                dialogTitle='Danh mục công đoạn'
                displayRelatedField={'ma_bp_data.ten_bp'}
                columnDisplay={'ma_bp_data.ma_bp'}
                searchEndpoint={`/${QUERY_KEYS.DANH_MUC_CONG_DOAN}`}
                searchColumns={congDoanSearchColumns}
                value={boPhanGT?.ma_bp_data?.ma_bp || ''}
                relatedFieldValue={boPhanGT?.ma_bp_data?.ten_bp || ''}
                onRowSelection={setBoPhanGT}
                disabled={isViewMode}
              />
            </div>

            {/* Hệ số */}
            <div className='flex items-center'>
              <Label className='min-w-40'>Hệ số</Label>
              <FormField name='he_so' type='number' className='w-40' disabled={isViewMode} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicInfo;
