import React from 'react';
import { InputTable } from '@/components/custom/arito/custom-input-table';
import InputTableActionBar from '../../InputTableActionBar';
import { getDocUserColumns } from './columns';

import { GridCellParams } from '@mui/x-data-grid';
import { FormMode } from '@/types/form';

interface SelectedCellInfo {
  id: string;
  field: string;
}

interface DocUserTabProps {
  mode: FormMode;
  rows: any[];
  selectedRowUuid?: string | null;
  selectedCell?: SelectedCellInfo | null;
  onRowClick?: (params: any) => void;
  onCellClick?: (params: GridCellParams) => void;
  onAddRow: () => void;
  onDeleteRow: () => void;
  onCopyRow: () => void;
  onMoveRow: (direction: 'up' | 'down') => void;
  onPin: () => void;
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void;
}

export const DocUserTab: React.FC<DocUserTabProps> = ({
  mode,
  rows,
  selectedRowUuid,
  onRowClick,
  onAddRow,
  onDeleteRow,
  onCopyRow,
  onMoveRow,
  onPin,
  onCellValueChange
}) => {
  return (
    <InputTable
      rows={rows}
      onRowClick={onRowClick}
      selectedRowId={selectedRowUuid || undefined}
      columns={getDocUserColumns(onCellValueChange)}
      getRowId={row => row?.uuid || ''}
      actionButtons={
        <InputTableActionBar
          mode={mode}
          handleAddRow={onAddRow}
          handleDeleteRow={onDeleteRow}
          handleCopyRow={onCopyRow}
          handleMoveRow={onMoveRow}
          handlePin={onPin}
        />
      }
    />
  );
};
