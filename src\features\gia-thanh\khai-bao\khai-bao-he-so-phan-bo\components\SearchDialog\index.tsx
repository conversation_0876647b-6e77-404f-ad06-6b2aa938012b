import { Button } from '@mui/material';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import { AritoIcon } from '@/components/custom/arito';
import { searchSchema } from './searchSchema';
import BasicForm from './BasicForm';

interface SearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

const SearchDialog = ({ open, onClose, onSubmit }: SearchDialogProps) => {
  const handleSubmit = async (data: any) => {
    try {
      console.log('>>>>>>>>>>>', data);
      onSubmit(data);
    } catch (err: any) {
      console.error('Error SEARCH form:', err);
    }
  };
  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title={'Khai báo hệ số phân bổ'}
      titleIcon={<AritoIcon icon={12} />}
      maxWidth='xl'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
    >
      <AritoForm
        mode={'search'}
        hasAritoActionBar={false}
        schema={searchSchema}
        onSubmit={handleSubmit}
        className='min-w-[50vw]'
        headerFields={<BasicForm />}
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={
          <>
            <div className='space-x-2 p-2'>
              <Button className='!hover:bg-main-light bg-main' type='submit' variant='contained'>
                <AritoIcon icon={884} marginX='4px' />
                Đồng ý
              </Button>

              <Button onClick={() => onClose()} variant='outlined'>
                <AritoIcon icon={885} marginX='4px' />
                Huỷ
              </Button>
            </div>
          </>
        }
      />
    </AritoDialog>
  );
};

export default SearchDialog;
