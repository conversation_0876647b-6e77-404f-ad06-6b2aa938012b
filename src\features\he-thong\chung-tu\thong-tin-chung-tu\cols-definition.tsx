import { GridCellParams, GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import { Checkbox } from '@/components/ui/checkbox';

export const DocumentsColumns = (handleOpenViewForm: (obj: any) => void): GridColDef[] => [
  {
    field: 'ma_ct',
    headerName: 'Mã chứng từ',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <div className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row)}>
        {params.value}
      </div>
    )
  },
  { field: 'ten_ct', headerName: 'Tên chứng từ', width: 250 },
  { field: 'ngay_ks', headerName: 'Ngày khoá sổ', width: 100 },
  {
    field: 'ct_nxd',
    headerName: 'Loại ct kho',
    width: 100,
    renderCell: (params: { row: { ct_nxd: string } }) => {
      const getValue = (ct_nxd: string) => {
        switch (ct_nxd) {
          case '0':
            return 'Khác';
          case '1':
            return 'Nhập';
          case '2':
            return 'Xuất';
          default:
            return '';
        }
      };
      return <div>{getValue(params.row.ct_nxd)}</div>;
    }
  }
];

export const TransactionColumns = (handleOpenViewForm: (obj: any) => void): GridColDef[] => [
  {
    field: 'ma_gd',
    headerName: 'Mã giao dịch',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <a className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row.ma_gd)}>
        {params.row.ma_gd}
      </a>
    )
  },
  { field: 'ten_gd', headerName: 'Tên giao dịch', width: 250 },
  { field: 'stt', headerName: 'Stt sắp xếp', width: 100 }
];

export const StatusColumns = (handleOpenViewForm: (obj: any) => void): GridColDef[] => [
  {
    field: 'ten_ttct',
    headerName: 'Tên trạng thái chứng từ',
    width: 250,
    renderCell: (params: GridRenderCellParams) => (
      <a
        className='cursor-pointer text-teal-600 hover:underline'
        onClick={() => handleOpenViewForm(params.row.ten_ttct)}
      >
        {params.row.ten_ttct}
      </a>
    )
  },
  {
    field: 'query_yn',
    headerName: 'Hiển thị tab',
    width: 100,
    type: 'boolean',
    renderCell: (params: GridCellParams) => {
      return <Checkbox checked={params.row.query_yn} disabled />;
    }
  },
  {
    field: 'mau_ttct',
    headerName: 'Màu',
    width: 100,
    renderCell: (params: GridCellParams) => {
      const colorValue = params.row.status || '#CCCCCC';
      return (
        <div
          className='mx-auto h-8 w-full border border-gray-300'
          style={{ backgroundColor: colorValue }}
          title={colorValue}
        />
      );
    }
  },
  { field: 'stt', headerName: 'Stt sắp xếp', width: 100 }
];

export const DocumentColumns = (handleOpenViewForm: (obj: any) => void): GridColDef[] => [
  {
    field: 'ma_nk',
    headerName: 'Mã quyển',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <a className='cursor-pointer text-teal-600 hover:underline' onClick={() => handleOpenViewForm(params.row.ma_nk)}>
        {params.row.ma_nk}
      </a>
    )
  },
  { field: 'ten_nk', headerName: 'Tên quyển', width: 250 },
  { field: 'so_ct_mau', headerName: 'Số khai báo', width: 100 },
  { field: 'ma_unit', headerName: 'Đơn vị', width: 100 },
  { field: 'kieu_trung_so', headerName: 'Kiểm tra trùng số', width: 100 }
];
