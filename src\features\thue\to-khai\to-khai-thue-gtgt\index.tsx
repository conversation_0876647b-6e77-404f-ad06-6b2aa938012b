'use client';

import React, { useState } from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import { lapToKhaiInitData, SearchFormValues } from './schema';
import LapToKhaiDialog from './components/lap-to-khai-dialog';
import SearchDialog from './components/search-dialog';
import ActionBar from './components/ActionBar';
import { useDialogState } from './hooks';
import { useTableData } from './hooks';

export default function ToKhaiThueGTGTPage() {
  const {
    initialSearchDialogOpen,
    showTable,
    lastSearchValues,
    isLapToKhaiDialogOpen,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    setShowTable,
    handleLapToKhaiDialogOpen,
    handleLapToKhaiDialogSubmit,
    handleLapToKhaiDialogClose
  } = useDialogState();

  const [selectedRow, setSelectedRow] = useState(null);

  const { tables, handleRowClick, updateTableColumns } = useTableData();

  function handleSearch(value: SearchFormValues) {
    handleInitialSearch(value);
  }

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      {showTable && (
        <div className='flex-1 overflow-hidden'>
          {' '}
          <ActionBar
            onSearchClick={handleSearchClick}
            onRefreshClick={() => {}}
            onLapToKhaiClick={handleLapToKhaiDialogOpen}
            searchParams={lastSearchValues}
          />
          <AritoDataTables tables={tables} onRowClick={setSelectedRow} />
        </div>
      )}

      <SearchDialog
        openSearchDialog={initialSearchDialogOpen}
        onCloseSearchDialog={handleInitialSearchClose}
        onSearch={handleSearch}
        updateTableColumns={() => {}}
      />

      <LapToKhaiDialog
        openDialog={isLapToKhaiDialogOpen}
        onCloseDialog={handleLapToKhaiDialogClose}
        onSubmit={handleLapToKhaiDialogSubmit}
        updateTableColumns={() => {}}
        initData={lapToKhaiInitData}
      />
    </div>
  );
}
