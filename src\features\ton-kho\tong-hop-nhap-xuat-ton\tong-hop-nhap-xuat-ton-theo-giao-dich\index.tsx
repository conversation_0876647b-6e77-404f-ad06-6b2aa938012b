'use client';

import React, { useState } from 'react';
import AritoDataTables from '@/components/custom/arito/data-tables';
import SearchDialog from './components/search-dialog';
import { mauSoLuongColumns } from './cols-definition';
import { useTableData } from './hooks/useTableData';
import ActionBar from './components/ActionBar';
import { useDialogState } from './hooks';

export default function TongHopNhapXuatTonTheoGiaoDichPage() {
  const {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    lastSearchValues,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,
    setShowTable
  } = useDialogState();

  const [selectedRow, setSelectedRow] = useState(null);
  const { tables, handleRowClick, updateTableColumns } = useTableData();

  return (
    <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
      <ActionBar
        onSearchClick={handleSearchClick}
        onEditPrintTemplateClick={handleEditPrintTemplateClick}
        onRefreshClick={() => {}}
        onFixedColumnsClick={() => {}}
        onExportDataClick={() => {}}
      />

      {showTable && (
        <div className='flex-1 overflow-hidden'>
          <AritoDataTables tables={tables} onRowClick={setSelectedRow} />
        </div>
      )}

      <SearchDialog
        openSearchDialog={initialSearchDialogOpen}
        onCloseSearchDialog={handleInitialSearchClose}
        onSearch={handleInitialSearch}
        updateTableColumns={updateTableColumns}
        initialData={lastSearchValues}
      />
    </div>
  );
}
