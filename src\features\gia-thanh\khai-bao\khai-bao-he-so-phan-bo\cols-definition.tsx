import { GridColDef } from '@mui/x-data-grid';

export function getDataTableColumns(): GridColDef[] {
  return [
    {
      field: 'unit_id_data',
      headerName: 'Đơn vị',
      width: 150,
      renderCell: (params: any) => {
        return params.row.unit_id_data?.ma_unit || '';
      }
    },
    {
      field: 'ma_yt_data',
      headerName: 'Mã yếu tố',
      width: 150,
      renderCell: (params: any) => {
        return params.row.ma_yt_data?.ma_yt || '';
      }
    },
    {
      field: 'ma_sp_data',
      headerName: 'Mã sản phẩm',
      width: 150,
      renderCell: (params: any) => {
        return params.row.ma_vt_data?.ma_vt || '';
      }
    },
    {
      field: 'ten_sp',
      headerName: 'Tên sản phẩm',
      width: 150,
      renderCell: (params: any) => {
        return params.row.ma_vt_data?.ten_vt || '';
      }
    },
    {
      field: 'ma_bp_data',
      headerName: 'Mã bộ phận',
      width: 150,
      renderCell: (params: any) => {
        return params.row.ma_bp_data?.ma_bp_data?.ma_bp || '';
      }
    },
    {
      field: 'so_lsx_data',
      headerName: 'Số lệnh sx',
      width: 150,
      renderCell: (params: any) => {
        return params.row.so_lsx_data?.so_lsx || '';
      }
    },
    {
      field: 'ma_vt_data',
      headerName: 'Mã vật tư',
      width: 150,
      renderCell: (params: any) => {
        return params.row.ma_vt_data?.ma_vt || '';
      }
    },
    {
      field: 'ten_vt',
      headerName: 'Tên vật tư',
      width: 150,
      renderCell: (params: any) => {
        return params.row.ma_vt_data?.ten_vt || '';
      }
    },
    {
      field: 'ma_bp0_data',
      headerName: 'Bp gián tiếp',
      width: 150,
      renderCell: (params: any) => {
        return params.row.ma_bp0_data?.ma_bp_data?.ma_bp || '';
      }
    },
    {
      field: 'he_so',
      headerName: 'Hệ số',
      width: 100
    }
  ];
}
