'use client';

import { useDialogContext } from '../contexts/DialogContext';

export const useStatusDialog = () => {
  const { statusDialog, openStatusDialog, closeStatusDialog } = useDialogContext();

  const openAddDialog = () => {
    openStatusDialog('add');
  };

  const openEditDialog = () => {
    openStatusDialog('edit');
  };

  const openViewDialog = () => {
    openStatusDialog('view');
  };

  return {
    isOpen: statusDialog.isOpen,
    formMode: statusDialog.formMode,
    openAddDialog,
    openEditDialog,
    openViewDialog,
    closeDialog: closeStatusDialog
  };
};
