import { But<PERSON> } from '@mui/material';
import React from 'react';
import { LapToKhaiFormValues, lapToKhaiInitData, lapToKhaiSchema } from '../../schema';
import { AritoHeaderTabs } from '@/components/custom/arito/header-tabs';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import BasicInfoTab from './BasicInfoTab';

interface LapToKhaiDialogProps {
  openDialog: boolean;
  onCloseDialog: () => void;
  onSubmit: (data: LapToKhaiFormValues) => void;
  updateTableColumns?: (templateValue: string) => void;
  initData?: LapToKhaiFormValues;
}

const LapToKhaiDialog: React.FC<LapToKhaiDialogProps> = ({
  openDialog,
  onCloseDialog,
  onSubmit,
  updateTableColumns,
  initData
}) => {
  const handleSubmit = (data: LapToKhaiFormValues) => {
    // Call the onSubmit callback with the form data
    onSubmit(data);

    // Close the dialog
    onCloseDialog();
  };

  return (
    <AritoDialog
      open={openDialog}
      onClose={onCloseDialog}
      title='Tờ khai thuế giá trị gia tăng'
      maxWidth='xl'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={lapToKhaiSchema}
        initialData={initData || lapToKhaiInitData}
        onSubmit={handleSubmit}
        className='w-[900px]'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
            <BasicInfoTab formMode='add' />
          </div>
        }
        bottomBar={
          <div className='flex justify-end space-x-2 p-4'>
          <Button variant='outlined' onClick={onCloseDialog}>
            Hủy
          </Button>
          <Button variant='contained' color='primary' type='submit'>
            Đồng ý
          </Button>
        </div>
        }
      >
      </AritoForm>
    </AritoDialog>
  );
};

export default LapToKhaiDialog;
