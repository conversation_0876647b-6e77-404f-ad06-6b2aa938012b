import { <PERSON>, FileText, Pencil, Plus, Trash } from 'lucide-react';
import { useEffect, useState } from 'react';
import { AritoActionButton } from '@/components/custom/arito/action-button';
import AritoMenuButton from '@/components/custom/arito/menu-button';
import AritoActionBar from '@/components/custom/arito/action-bar';
import { DonViCoSo } from '@/types/schemas/don-vi-co-so.type';
import AritoIcon from '@/components/custom/arito/icon';
import { useDonViCoSo } from '@/hooks';

interface ActionBarProps {
  onAddClick: () => void;
  onEditClick: () => void;
  onDeleteClick: () => void;
  onCopyClick: () => void;
  onViewClick: () => void;
  onRefreshClick: () => void;
  onSearchClick: () => void;
  searchData?: any; // Optional search data from the search form
}

const ActionBar = ({
  onAddClick,
  onEditClick,
  onDeleteClick,
  onCopyClick,
  onViewClick,
  onRefreshClick,
  onSearchClick,
  searchData
}: ActionBarProps) => {
  const donVi = searchData?.don_vi;
  const { donViCoSos } = useDonViCoSo();
  const [donViData, setDonViData] = useState<DonViCoSo | null>(null);

  useEffect(() => {
    if (donVi && donViCoSos.length > 0) {
      const selectedDonVi = donViCoSos.find(item => item.uuid === donVi);
      if (selectedDonVi) {
        setDonViData(selectedDonVi);
      }
    }
  }, [donVi, donViCoSos]);

  return (
    <AritoActionBar
      titleComponent={
        <div>
          <h1 className='my-1.5 text-xl font-bold'>Khai báo hệ số phân bổ</h1>
          {searchData && donViData && (
            <div className='text-[10px] text-gray-500'>
              <p>
                <span className='mr-2 inline-block h-2 w-2 rounded-full bg-red-600'></span>
                <span className='font-semibold'>
                  Đơn vị: [{donViData.ma_unit} - {donViData.ten_unit}] kỳ {searchData?.ky} năm {searchData?.nam}
                </span>
              </p>
            </div>
          )}
        </div>
      }
    >
      <AritoActionButton title='Thêm' icon={Plus} onClick={onAddClick} variant='primary' />
      <AritoActionButton title='Sửa' icon={Pencil} onClick={onEditClick} />
      <AritoActionButton title='Xoá' variant='destructive' icon={Trash} onClick={onDeleteClick} />
      <AritoActionButton title='Sao chép' icon={FileText} onClick={onCopyClick} />
      <AritoActionButton title='Xem' icon={Eye} onClick={onViewClick} />
      <AritoMenuButton
        items={[
          {
            title: 'Tìm kiếm',
            icon: <AritoIcon icon={19} />,
            onClick: onSearchClick,
            group: 0
          },
          {
            title: 'Refresh',
            icon: <AritoIcon icon={15} />,
            onClick: onRefreshClick,
            group: 0
          },
          {
            title: 'Cố định cột',
            icon: <AritoIcon icon={16} />,
            onClick: () => {},
            group: 0
          },
          {
            title: 'Kết xuất dữ liệu',
            icon: <AritoIcon icon={555} />,
            onClick: () => {},
            group: 1
          }
        ]}
      />
    </AritoActionBar>
  );
};

export default ActionBar;
