'use client';

import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import { FormField } from '@/components/custom/arito/form/form-field';
import { donViSearchColumns } from '@/constants/search-columns';
import { DonViCoSo } from '@/types/schemas/don-vi-co-so.type';
import QUERY_KEYS from '@/constants/query-keys';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface GeneralInfoTabProps {
  formMode: FormMode;
  donViCoSo: DonViCoSo | null;
  setDonViCoSo: (donVi: DonViCoSo) => void;
}

export const GeneralInfoTab: React.FC<GeneralInfoTabProps> = ({ formMode, donViCoSo, setDonViCoSo }) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='space-y-1'>
        <div className='flex items-center'>
          <Label className='w-40 min-w-40'><PERSON><PERSON> chứng từ mẫu</Label>
          <div className='w-[200px]'>
            <FormField type='number' label='' name='so_ct_mau' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Đơn vị</Label>
          <SearchField<DonViCoSo>
            type='text'
            name='ma_unit'
            disabled={true}
            searchEndpoint={`/${QUERY_KEYS.DON_VI}/`}
            searchColumns={donViSearchColumns}
            dialogTitle='Danh mục đơn vị'
            columnDisplay='ma_unit'
            displayRelatedField='ten_unit'
            classNameRelatedField='w-full'
            onRowSelection={setDonViCoSo}
            value={donViCoSo?.ma_unit || ''}
          />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Kiểm tra trùng số</Label>
          <div className='w-[300px]'>
            <FormField
              type='select'
              label=''
              name='kieu_trung_so'
              disabled={formMode === 'view'}
              options={[
                { value: '0', label: '0. Không kiểm tra' },
                { value: '1', label: '1. Theo ngày' },
                { value: '2', label: '2. Theo tháng' },
                { value: '3', label: '3. Theo quý' },
                { value: '4', label: '4. Theo năm' },
                { value: '5', label: '5. Tất' }
              ]}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số hiện tại</Label>
          <div className='w-[300px]'>
            <FormField type='number' name='i_so_ct_ht' disabled={formMode === 'view'} />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Hiệu lực từ/đến</Label>
          <FormField type='date' name='ngay_hl1' disabled={formMode === 'view'} />
          <FormField type='date' name='ngay_hl2' disabled={formMode === 'view'} className='ml-2' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Ký hiệu</Label>
          <FormField type='text' name='so_ct2' disabled={formMode === 'view'} />
        </div>

        <div className='flex items-center'>
          <Label className='w-32 min-w-32'>Trạng thái</Label>
          <div className='w-[350px]'>
            <FormField
              type='select'
              name='status'
              disabled={formMode === 'view'}
              options={[
                { label: '1. Còn sử dụng', value: '1' },
                { label: '0. Không sử dụng', value: '0' }
              ]}
              defaultValue={'1'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
