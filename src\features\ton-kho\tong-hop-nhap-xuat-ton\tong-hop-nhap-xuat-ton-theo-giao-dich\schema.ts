import { z } from 'zod';

export const searchSchema = z.object({
  ngay_ct1: z.string().optional(),
  ngay_ct2: z.string().optional(),
  ma_kho: z.string().optional(),
  nhom_kho_yn: z.union([z.boolean(), z.number()]).optional(),
  ma_gd: z.string().optional(),
  ma_vt: z.string().optional(),
  ma_lo: z.string().optional(),
  ma_vi_tri: z.string().optional(),
  ma_lvt: z.string().optional(),
  ton_kho_yn: z.union([z.boolean(), z.number()]).optional(),
  nh_vt1: z.string().optional(),
  nh_vt2: z.string().optional(),
  nh_vt3: z.string().optional(),
  ma_unit: z.string().optional(),
  loai_du_lieu: z.number().optional(),
  mau_bc: z.string().optional(),
  tk_vt: z.string().optional(),
  tk_ht: z.string().optional(),
  tk_gv: z.string().optional(),
  ma_gd0: z.string().optional(),
  dvt: z.string().optional(),
  vt_ton: z.string().optional(),
  group_by: z.string().optional(),
  ps_dc: z.string().optional(),
  data_analysis_struct: z.string().optional()
});

export type SearchFormValues = z.infer<typeof searchSchema>;

export const initialSearchValues: SearchFormValues = {
  ngay_ct1: '',
  ngay_ct2: '',
  ma_kho: '',
  nhom_kho_yn: 0,
  ma_gd: '',
  ma_vt: '',
  ma_lo: '',
  ma_vi_tri: '',
  ma_lvt: '',
  ton_kho_yn: 1,
  nh_vt1: '',
  nh_vt2: '',
  nh_vt3: '',
  ma_unit: '',
  loai_du_lieu: 1,
  mau_bc: '10',
  tk_vt: '',
  tk_ht: '',
  tk_gv: '',
  ma_gd0: '',
  dvt: '',
  vt_ton: '',
  group_by: '',
  ps_dc: '1'
};
