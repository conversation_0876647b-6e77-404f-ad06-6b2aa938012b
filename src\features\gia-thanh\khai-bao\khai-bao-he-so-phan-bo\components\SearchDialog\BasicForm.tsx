import { useState } from 'react';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';
import { useDonViCoSo } from '@/hooks';

function BasicForm() {
  const { donViCoSos, isLoading } = useDonViCoSo();

  // options
  const donViOptions = donViCoSos.map(donVi => ({
    value: donVi.uuid,
    label: `${donVi.ma_unit} - ${donVi.ten_unit}`
  }));

  return (
    <div className='max-h-[calc(100vh-150px)] overflow-y-auto'>
      <div className='p-6'>
        <div className='flex flex-col gap-y-3'>
          {/* Kỳ */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'><PERSON><PERSON></Label>
            <FormField name='ky' type='number' className='w-40' />
          </div>

          {/* Năm */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Năm</Label>
            <FormField name='nam' type='number' className='w-40' />
          </div>

          {/* Đơn vị */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Đơn vị</Label>
            {/* Đơn vị */}
            <div className='flex items-center'>
              <FormField
                type='select'
                className='w-96'
                name='don_vi'
                options={donViOptions}
                defaultValue={donViOptions[0]?.value || ''}
                disabled={isLoading}
              />
            </div>
          </div>

          {/* Sao chép */}
          <div className='flex flex-col sm:flex-row sm:items-center'>
            <Label className='mb-2 sm:mb-0 sm:w-40 sm:min-w-40'>Sao chép</Label>
            <FormField
              name='sao_chep'
              type='select'
              className='w-40'
              options={[
                { value: '0', label: '0. Không' },
                { value: '1', label: '1. Chép từ kỳ trước' }
              ]}
              defaultValue='0'
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default BasicForm;
