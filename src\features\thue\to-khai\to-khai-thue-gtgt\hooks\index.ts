import { useState } from 'react';
import { SearchFormValues, LapToKhaiFormValues } from '../schema';

export function useDialogState() {
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [showTable, setShowTable] = useState(false);
  const [lastSearchValues, setLastSearchValues] = useState<SearchFormValues | null>(null);
  const [isLapToKhaiDialogOpen, setIsLapToKhaiDialogOpen] = useState(false);

  const handleInitialSearchClose = () => {
    setInitialSearchDialogOpen(false);
  };

  const handleInitialSearch = (values: SearchFormValues) => {
    setLastSearchValues(values);
    setInitialSearchDialogOpen(false);
    setShowTable(true);
  };

  const handleSearchClick = () => {
    setInitialSearchDialogOpen(true);
  };

  const handleLapToKhaiDialogOpen = () => {
    setIsLapToKhaiDialogOpen(true);
  };

  const handleLapToKhaiDialogClose = () => {
    setIsLapToKhaiDialogOpen(false);
  };

  const handleLapToKhaiDialogSubmit = (data: LapToKhaiFormValues) => {
    console.log('Lap to khai data:', data);
    setIsLapToKhaiDialogOpen(false);
  };

  return {
    initialSearchDialogOpen,
    showTable,
    lastSearchValues,
    isLapToKhaiDialogOpen,
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    setShowTable,
    handleLapToKhaiDialogOpen,
    handleLapToKhaiDialogSubmit,
    handleLapToKhaiDialogClose
  };
}

export function useTableData() {
  const [tables] = useState([
    {
      name: 'Tờ khai thuế GTGT',
      rows: [],
      columns: []
    }
  ]);

  const handleRowClick = (row: any) => {
    console.log('Row clicked:', row);
  };

  const updateTableColumns = (templateValue: string) => {
    console.log('Update table columns:', templateValue);
  };

  return {
    tables,
    handleRowClick,
    updateTableColumns
  };
}
